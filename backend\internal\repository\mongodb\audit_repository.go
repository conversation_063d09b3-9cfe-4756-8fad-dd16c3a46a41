package mongodb

import (
	"context"
	"fmt"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/model"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type AuditRepository struct {
	auditCollection  *mongo.Collection
	systemCollection *mongo.Collection
}

func NewAuditRepository() *AuditRepository {
	return &AuditRepository{
		auditCollection:  config.GetCollection("audit_logs"),
		systemCollection: config.GetCollection("system_logs"),
	}
}

// CreateAuditLog creates a new audit log entry
func (r *AuditRepository) CreateAuditLog(ctx context.Context, log *model.AuditLog) error {
	log.BeforeInsert()
	
	result, err := r.auditCollection.InsertOne(ctx, log)
	if err != nil {
		return fmt.Errorf("failed to create audit log: %w", err)
	}
	
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		log.ID = oid
	}
	
	return nil
}

// CreateSystemLog creates a new system log entry
func (r *AuditRepository) CreateSystemLog(ctx context.Context, log *model.SystemLog) error {
	log.BeforeInsert()
	
	result, err := r.systemCollection.InsertOne(ctx, log)
	if err != nil {
		return fmt.Errorf("failed to create system log: %w", err)
	}
	
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		log.ID = oid
	}
	
	return nil
}

// GetAuditLogsByUserID retrieves audit logs for a specific user
func (r *AuditRepository) GetAuditLogsByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*model.AuditLog, error) {
	filter := bson.M{"user_id": userID}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})
	
	cursor, err := r.auditCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by user ID: %w", err)
	}
	defer cursor.Close(ctx)
	
	var logs []*model.AuditLog
	for cursor.Next(ctx) {
		var log model.AuditLog
		if err := cursor.Decode(&log); err != nil {
			return nil, fmt.Errorf("failed to decode audit log: %w", err)
		}
		logs = append(logs, &log)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return logs, nil
}

// GetAuditLogsByAction retrieves audit logs by action type
func (r *AuditRepository) GetAuditLogsByAction(ctx context.Context, action string, limit, offset int) ([]*model.AuditLog, error) {
	filter := bson.M{"action": action}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})
	
	cursor, err := r.auditCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by action: %w", err)
	}
	defer cursor.Close(ctx)
	
	var logs []*model.AuditLog
	for cursor.Next(ctx) {
		var log model.AuditLog
		if err := cursor.Decode(&log); err != nil {
			return nil, fmt.Errorf("failed to decode audit log: %w", err)
		}
		logs = append(logs, &log)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return logs, nil
}

// GetAuditLogsByDateRange retrieves audit logs within a date range
func (r *AuditRepository) GetAuditLogsByDateRange(ctx context.Context, startDate, endDate time.Time, limit, offset int) ([]*model.AuditLog, error) {
	filter := bson.M{
		"created_at": bson.M{
			"$gte": startDate,
			"$lte": endDate,
		},
	}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})
	
	cursor, err := r.auditCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by date range: %w", err)
	}
	defer cursor.Close(ctx)
	
	var logs []*model.AuditLog
	for cursor.Next(ctx) {
		var log model.AuditLog
		if err := cursor.Decode(&log); err != nil {
			return nil, fmt.Errorf("failed to decode audit log: %w", err)
		}
		logs = append(logs, &log)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return logs, nil
}

// GetSystemLogsByLevel retrieves system logs by level
func (r *AuditRepository) GetSystemLogsByLevel(ctx context.Context, level string, limit, offset int) ([]*model.SystemLog, error) {
	filter := bson.M{"level": level}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})
	
	cursor, err := r.systemCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get system logs by level: %w", err)
	}
	defer cursor.Close(ctx)
	
	var logs []*model.SystemLog
	for cursor.Next(ctx) {
		var log model.SystemLog
		if err := cursor.Decode(&log); err != nil {
			return nil, fmt.Errorf("failed to decode system log: %w", err)
		}
		logs = append(logs, &log)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return logs, nil
}

// GetSystemLogsByComponent retrieves system logs by component
func (r *AuditRepository) GetSystemLogsByComponent(ctx context.Context, component string, limit, offset int) ([]*model.SystemLog, error) {
	filter := bson.M{"component": component}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})
	
	cursor, err := r.systemCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get system logs by component: %w", err)
	}
	defer cursor.Close(ctx)
	
	var logs []*model.SystemLog
	for cursor.Next(ctx) {
		var log model.SystemLog
		if err := cursor.Decode(&log); err != nil {
			return nil, fmt.Errorf("failed to decode system log: %w", err)
		}
		logs = append(logs, &log)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return logs, nil
}

// DeleteOldAuditLogs deletes audit logs older than the specified duration
func (r *AuditRepository) DeleteOldAuditLogs(ctx context.Context, olderThan time.Duration) error {
	cutoff := time.Now().UTC().Add(-olderThan)
	filter := bson.M{
		"created_at": bson.M{"$lt": cutoff},
	}
	
	_, err := r.auditCollection.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete old audit logs: %w", err)
	}
	
	return nil
}

// DeleteOldSystemLogs deletes system logs older than the specified duration
func (r *AuditRepository) DeleteOldSystemLogs(ctx context.Context, olderThan time.Duration) error {
	cutoff := time.Now().UTC().Add(-olderThan)
	filter := bson.M{
		"created_at": bson.M{"$lt": cutoff},
	}
	
	_, err := r.systemCollection.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete old system logs: %w", err)
	}
	
	return nil
}

// CountAuditLogs returns the total number of audit logs
func (r *AuditRepository) CountAuditLogs(ctx context.Context) (int64, error) {
	count, err := r.auditCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count audit logs: %w", err)
	}
	
	return count, nil
}

// CountSystemLogs returns the total number of system logs
func (r *AuditRepository) CountSystemLogs(ctx context.Context) (int64, error) {
	count, err := r.systemCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count system logs: %w", err)
	}
	
	return count, nil
}

// GetUserAuditLogs retrieves audit logs for a specific user by email
func (r *AuditRepository) GetUserAuditLogs(ctx context.Context, userEmail string, limit, offset int) ([]*model.AuditLog, error) {
	filter := bson.M{"user_email": userEmail}

	opts := options.Find().
		SetLimit(int64(limit)).
		SetSkip(int64(offset)).
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.auditCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find user audit logs: %w", err)
	}
	defer cursor.Close(ctx)

	var logs []*model.AuditLog
	if err = cursor.All(ctx, &logs); err != nil {
		return nil, fmt.Errorf("failed to decode user audit logs: %w", err)
	}

	return logs, nil
}

// GetAuditLogs retrieves all audit logs with pagination
func (r *AuditRepository) GetAuditLogs(ctx context.Context, limit, offset int) ([]*model.AuditLog, error) {
	opts := options.Find().
		SetLimit(int64(limit)).
		SetSkip(int64(offset)).
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.auditCollection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find audit logs: %w", err)
	}
	defer cursor.Close(ctx)

	var logs []*model.AuditLog
	if err = cursor.All(ctx, &logs); err != nil {
		return nil, fmt.Errorf("failed to decode audit logs: %w", err)
	}

	return logs, nil
}
