#!/bin/bash

# TrueDax Web Scraper - Integration Test Script

echo "🧪 Running TrueDax Web Scraper Integration Tests..."

API_URL="http://localhost:9000"

# Test 1: Health Check
echo "🏥 Testing health endpoint..."
if curl -f -s "$API_URL/health" > /dev/null; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

# Test 2: Get JWT Token (Login)
echo "🔐 Testing authentication..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_URL/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"password"}')

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "⚠️  Authentication test skipped (login endpoint may require proper setup)"
    # Use a dummy token for testing
    TOKEN="dummy-token-for-testing"
else
    echo "✅ Authentication successful"
fi

# Test 3: Test comprehensive scraping (no selectors)
echo "🕸️  Testing comprehensive scraping..."
SCRAPE_RESPONSE=$(curl -s -X POST "$API_URL/jobs/test-scrape" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "url": "https://example.com",
        "comprehensive_mode": true,
        "javascript_enabled": false
    }')

if echo $SCRAPE_RESPONSE | grep -q '"success":true'; then
    echo "✅ Comprehensive scraping test passed"
    echo "📊 Sample data extracted:"
    echo $SCRAPE_RESPONSE | grep -o '"title":"[^"]*' | head -1
else
    echo "⚠️  Scraping test returned errors (this may be expected for auth-protected endpoints)"
    echo "Response preview: $(echo $SCRAPE_RESPONSE | head -c 200)..."
fi

# Test 4: Swagger Documentation
echo "📚 Testing Swagger documentation..."
if curl -f -s "$API_URL/swagger/index.html" > /dev/null; then
    echo "✅ Swagger documentation accessible"
else
    echo "❌ Swagger documentation not accessible"
fi

# Test 5: Container Health
echo "🐳 Checking container health..."
CONTAINER_STATUS=$(docker-compose ps -q truedax-scraper | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null)
if [ "$CONTAINER_STATUS" = "healthy" ]; then
    echo "✅ Container is healthy"
else
    echo "⚠️  Container health status: $CONTAINER_STATUS"
fi

echo ""
echo "🎉 Integration tests completed!"
echo ""
echo "📋 Service Status:"
echo "   API URL: $API_URL"
echo "   Swagger: $API_URL/swagger/index.html"
echo "   Health: $API_URL/health"
echo ""
echo "🔧 Quick Commands:"
echo "   View logs: docker-compose logs -f truedax-scraper"
echo "   Stop service: docker-compose down"
echo "   Restart: docker-compose restart"
