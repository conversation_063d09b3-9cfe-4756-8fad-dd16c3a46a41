# Database Schema Design - TrueDax Web Scraper

## 🎯 **Revised Approach: Full Database Storage**

After analyzing your actual scraping results (164KB for single Amazon page), storing everything in database makes much more sense than S3 hybrid approach.

### **Real Data Analysis:**
- **Single page**: 164KB (2,079 lines of rich data)
- **100 pages**: ~16MB per job
- **1,000 jobs/month**: ~16GB/month
- **Database compression**: Can reduce by 60-80%

## 🗄️ **Complete Database Schema**

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    api_key VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Jobs table
CREATE TABLE jobs (
    id VARCHAR(50) PRIMARY KEY,
    user_id UUID NOT NULL,
    name VARCHA<PERSON>(255) NOT NULL,
    description TEXT,
    config J<PERSON>NB NOT NULL,
    status VARCHAR(20) NOT NULL,
    priority INTEGER DEFAULT 2,
    progress INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_msg TEXT,
    result_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2),
    total_load_time_ms BIGINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Scraped pages (individual page results)
CREATE TABLE scraped_pages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id VARCHAR(50) NOT NULL,
    url TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    error_msg TEXT,
    load_time_ms INTEGER,
    scraped_at TIMESTAMP DEFAULT NOW(),
    content_length INTEGER,
    word_count INTEGER,
    heading_count INTEGER DEFAULT 0,
    image_count INTEGER DEFAULT 0,
    link_count INTEGER DEFAULT 0,
    form_count INTEGER DEFAULT 0,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
);

-- Extracted content (structured data from pages)
CREATE TABLE page_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID NOT NULL,
    title TEXT,
    description TEXT,
    all_text TEXT, -- Full text for search
    main_content TEXT, -- Smart extracted main content
    metadata JSONB, -- Technical metadata (_metadata from your results)
    extraction_method VARCHAR(50),
    has_structured_data BOOLEAN DEFAULT false,
    FOREIGN KEY (page_id) REFERENCES scraped_pages(id) ON DELETE CASCADE
);

-- Headings extracted from pages
CREATE TABLE page_headings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID NOT NULL,
    level INTEGER NOT NULL,
    text TEXT NOT NULL,
    tag VARCHAR(10),
    position_index INTEGER, -- Order on page
    FOREIGN KEY (page_id) REFERENCES scraped_pages(id) ON DELETE CASCADE
);

-- Images found on pages
CREATE TABLE page_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID NOT NULL,
    src TEXT NOT NULL,
    alt TEXT,
    width VARCHAR(20),
    height VARCHAR(20),
    position_index INTEGER,
    FOREIGN KEY (page_id) REFERENCES scraped_pages(id) ON DELETE CASCADE
);

-- Links found on pages
CREATE TABLE page_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID NOT NULL,
    href TEXT NOT NULL,
    text TEXT,
    title TEXT,
    is_external BOOLEAN,
    position_index INTEGER,
    FOREIGN KEY (page_id) REFERENCES scraped_pages(id) ON DELETE CASCADE
);

-- Forms found on pages
CREATE TABLE page_forms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID NOT NULL,
    action TEXT,
    method VARCHAR(10),
    inputs JSONB, -- Array of input elements
    position_index INTEGER,
    FOREIGN KEY (page_id) REFERENCES scraped_pages(id) ON DELETE CASCADE
);

-- Custom extracted data (based on user-defined selectors)
CREATE TABLE extracted_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID NOT NULL,
    selector_name VARCHAR(100) NOT NULL,
    selector_query VARCHAR(500) NOT NULL,
    value TEXT,
    data_type VARCHAR(50), -- 'text', 'price', 'date', 'number', etc.
    confidence_score DECIMAL(3,2), -- How confident we are in extraction
    FOREIGN KEY (page_id) REFERENCES scraped_pages(id) ON DELETE CASCADE
);

-- Job queue for background processing
CREATE TABLE job_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id VARCHAR(50) NOT NULL,
    priority INTEGER NOT NULL,
    scheduled_at TIMESTAMP DEFAULT NOW(),
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    last_error TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
);

-- Audit log for tracking changes
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 📊 **Indexes for Performance**

```sql
-- Primary performance indexes
CREATE INDEX idx_jobs_user_id ON jobs(user_id);
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_created_at ON jobs(created_at DESC);
CREATE INDEX idx_jobs_priority_status ON jobs(priority DESC, status);

CREATE INDEX idx_scraped_pages_job_id ON scraped_pages(job_id);
CREATE INDEX idx_scraped_pages_url ON scraped_pages USING hash(url);
CREATE INDEX idx_scraped_pages_success ON scraped_pages(success);
CREATE INDEX idx_scraped_pages_scraped_at ON scraped_pages(scraped_at DESC);

CREATE INDEX idx_page_content_page_id ON page_content(page_id);
CREATE INDEX idx_page_content_title ON page_content USING gin(to_tsvector('english', title));

CREATE INDEX idx_page_headings_page_id ON page_headings(page_id);
CREATE INDEX idx_page_headings_level ON page_headings(level);
CREATE INDEX idx_page_headings_text ON page_headings USING gin(to_tsvector('english', text));

CREATE INDEX idx_page_images_page_id ON page_images(page_id);
CREATE INDEX idx_page_links_page_id ON page_links(page_id);
CREATE INDEX idx_page_forms_page_id ON page_forms(page_id);

CREATE INDEX idx_extracted_data_page_id ON extracted_data(page_id);
CREATE INDEX idx_extracted_data_selector ON extracted_data(selector_name);
CREATE INDEX idx_extracted_data_value ON extracted_data USING gin(to_tsvector('english', value));

CREATE INDEX idx_job_queue_priority ON job_queue(priority DESC, scheduled_at);
CREATE INDEX idx_audit_log_user_id ON audit_log(user_id, created_at DESC);

-- Full-text search indexes
CREATE INDEX idx_page_content_fulltext ON page_content USING gin(to_tsvector('english', 
    COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(all_text, '')));
```

## 🚀 **Benefits of This Approach**

### **1. Rich Querying Capabilities**
```sql
-- Find all pages with specific headings
SELECT p.url, h.text 
FROM scraped_pages p 
JOIN page_headings h ON p.id = h.page_id 
WHERE h.text ILIKE '%PlayStation%';

-- Search across all scraped content
SELECT p.url, pc.title 
FROM scraped_pages p 
JOIN page_content pc ON p.id = pc.page_id 
WHERE to_tsvector('english', pc.all_text) @@ plainto_tsquery('gaming headphones');

-- Analyze success rates by domain
SELECT 
    SUBSTRING(url FROM 'https?://([^/]+)') as domain,
    COUNT(*) as total_pages,
    AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END) as success_rate
FROM scraped_pages 
GROUP BY domain;
```

### **2. Analytics & Reporting**
```sql
-- Job performance metrics
SELECT 
    DATE(created_at) as date,
    COUNT(*) as jobs_created,
    AVG(result_count) as avg_pages_per_job,
    AVG(success_rate) as avg_success_rate
FROM jobs 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at);
```

### **3. Data Export Flexibility**
- Export to CSV/Excel with custom queries
- Real-time API responses
- Filtered data exports
- Aggregated reports

## 💾 **Storage Estimation**

### **Per Page (based on your Amazon example):**
- `scraped_pages`: ~200 bytes
- `page_content`: ~50KB (compressed text)
- `page_headings`: ~2KB (35 headings)
- `page_images`: ~15KB (139 images)
- `page_links`: ~10KB (232 links)
- `page_forms`: ~500 bytes
- `extracted_data`: ~2KB (custom selectors)

**Total per page: ~70KB** (vs 164KB JSON file = 57% compression)

### **Monthly Estimates:**
- **1,000 jobs** × **100 pages** = **7GB/month**
- **PostgreSQL compression**: ~3-4GB actual storage
- **Much more queryable and useful than flat files**

This approach gives you a proper data warehouse for web scraping with full SQL capabilities!
