version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: truedax-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: truedax_user
      MONGO_INITDB_ROOT_PASSWORD: truedax_secure_password_2024
      MONGO_INITDB_DATABASE: truedax_scraper
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./data/mongodb-logs:/var/log/mongodb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - scraper-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M

  truedax-scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: truedax-web-scraper
    ports:
      - "9000:9000"
    environment:
      # Server Configuration
      - PORT=9000
      - GIN_MODE=release

      # Database Configuration
      - MONGODB_URI=**************************************************************************************************
      - MONGODB_HOST=mongodb
      - MONGODB_PORT=27017
      - MONGODB_DATABASE=truedax_scraper
      - MONGODB_USERNAME=truedax_user
      - MONGODB_PASSWORD=truedax_secure_password_2024
      - MONGODB_MAX_POOL_SIZE=100
      - MONGODB_MIN_POOL_SIZE=5
      - MONGODB_TIMEOUT=10s

      # JWT Configuration
      - JWT_SECRET=your-super-secure-jwt-secret-key-change-in-production-2024
      - JWT_ACCESS_TOKEN_DURATION=15m
      - JWT_REFRESH_TOKEN_DURATION=7d

      # Scraper Configuration
      - MAX_CONCURRENT_JOBS=5
      - RESULTS_DIRECTORY=/app/results
      - CLEANUP_INTERVAL=24h
      - MAX_RESULT_AGE=720h

      # Chrome Configuration for JavaScript scraping
      - CHROME_BIN=/usr/bin/chromium-browser
      - CHROME_PATH=/usr/bin/chromium-browser
      - CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless

      # Logging Configuration
      - LOG_LEVEL=info
      - LOG_FORMAT=json
      - LOG_OUTPUT=stdout

    volumes:
      # Persist results data
      - ./data/results:/app/results
      # Mount logs directory
      - ./data/logs:/app/logs

    depends_on:
      mongodb:
        condition: service_healthy
    restart: unless-stopped

    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

    networks:
      - scraper-network

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: truedax-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - truedax-scraper
    restart: unless-stopped
    networks:
      - scraper-network
    profiles:
      - production

networks:
  scraper-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
  results-data:
    driver: local
