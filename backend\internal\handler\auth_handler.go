package handler

import (
	"net/http"
	"strings"

	"go-rest-api/internal/services/auth"
	"go-rest-api/config"
	"go-rest-api/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	authService *auth.AuthService
}

// NewAuthHandler creates a new AuthHandler
func NewAuthHandler(config *config.Config, repos *repository.Repositories) *AuthHandler {
	return &AuthHandler{
		authService: auth.NewAuthService(config, repos),
	}
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error string `json:"error" example:"Invalid credentials"`
}



// Login godoc
// @Summary      User login
// @Description  Authenticate user with email and password, returns access and refresh tokens
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request body auth.LoginRequest true "Login credentials"
// @Success      200 {object} auth.TokenPair
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request format"})
		return
	}

	// Get client IP
	ipAddress := c.ClientIP()

	// Authenticate user
	tokenPair, err := h.authService.Login(req, ipAddress)
	if err != nil {
		if strings.Contains(err.Error(), "invalid credentials") ||
		   strings.Contains(err.Error(), "account is locked") ||
		   strings.Contains(err.Error(), "account is disabled") {
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Authentication failed"})
		return
	}
c.JSON(http.StatusOK, tokenPair)
}

// Register godoc
// @Summary      User registration
// @Description  Register a new user account
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request body auth.RegisterRequest true "Registration details"
// @Success      201 {object} model.User
// @Failure      400 {object} ErrorResponse
// @Failure      409 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req auth.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request format"})
		return
	}

	// Register user
	user, err := h.authService.Register(req)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			c.JSON(http.StatusConflict, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Registration failed"})
		return
	}

	c.JSON(http.StatusCreated, user)
}

// RefreshToken godoc
// @Summary      Refresh access token
// @Description  Refresh an access token using a refresh token
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request body auth.RefreshRequest true "Refresh token"
// @Success      200 {object} auth.TokenPair
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req auth.RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request format"})
		return
	}

	// Get client IP
	ipAddress := c.ClientIP()

	// Refresh token
	tokenPair, err := h.authService.RefreshToken(req, ipAddress)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid or expired refresh token"})
		return
	}

	c.JSON(http.StatusOK, tokenPair)
}

// Logout godoc
// @Summary      User logout
// @Description  Logout user and revoke all refresh tokens
// @Tags         auth
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200 {object} MessageResponse
// @Failure      401 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// Get user ID from context (set by middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	// Logout user
	if err := h.authService.Logout(userID.(uuid.UUID)); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Logout failed"})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{Message: "Logged out successfully"})
}

// Legacy Login function for backward compatibility
// This will be removed in future versions
func Login(c *gin.Context) {
	// For now, return an error directing users to the new endpoint
	c.JSON(http.StatusGone, ErrorResponse{
		Error: "This endpoint is deprecated. Please use /auth/login instead",
	})
}