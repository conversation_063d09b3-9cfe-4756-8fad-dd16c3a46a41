# TrueDax Web Scraper - Docker Deployment Guide

This guide covers the complete Docker deployment of the TrueDax Web Scraper for both development and production environments.

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM available
- 10GB+ disk space

### Development Deployment

**Windows:**
```bash
.\scripts\deploy-dev.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/deploy-dev.sh
./scripts/deploy-dev.sh
```

**Manual:**
```bash
# Copy environment file
cp .env.example .env

# Start services
docker-compose up --build -d

# Check health
curl http://localhost:9000/health
```

## 📋 Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│     Nginx       │────│  TrueDax API    │────│   File System  │
│   (Reverse      │    │   (Go Service)  │    │   (Results)     │
│    Proxy)       │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
   Port 80/443              Port 9000              Volume Mount
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and customize:

```bash
# Server Configuration
PORT=9000
GIN_MODE=release


# Scraper Configuration
MAX_CONCURRENT_JOBS=5
RESULTS_DIRECTORY=/app/results
CLEANUP_INTERVAL=24h
MAX_RESULT_AGE=720h

# Chrome Configuration
CHROME_BIN=/usr/bin/chromium-browser
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless
```

### Docker Compose Profiles

- **Default**: Basic scraper service only
- **Production**: Includes Nginx reverse proxy with SSL

## 🔒 Production Deployment

### 1. SSL Certificates

**Option A: Let's Encrypt (Recommended)**
```bash
# Install certbot
sudo apt-get install certbot

# Get certificates
sudo certbot certonly --standalone -d your-domain.com

# Copy to nginx directory
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem
```

**Option B: Self-signed (Development only)**
```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem \
    -subj "/C=US/ST=State/L=City/O=TrueDax/CN=localhost"
```

### 2. Production Deployment

```bash
# Set production environment
export COMPOSE_PROFILES=production

# Deploy
./scripts/deploy-prod.sh
```

### 3. Security Checklist

- [ ] Configure proper SSL certificates
- [ ] Set up firewall rules
- [ ] Configure log rotation
- [ ] Set up monitoring
- [ ] Configure backup strategy
- [ ] Review rate limiting settings

## 📊 Monitoring & Management

### Health Checks

```bash
# Basic health check
curl http://localhost:9000/health

# With nginx proxy
curl http://localhost/health

# Detailed service status
docker-compose ps
docker-compose logs -f truedax-scraper
```

### Resource Monitoring

```bash
# Container resource usage
docker stats

# Disk usage
du -sh data/results/

# Memory usage by container
docker exec truedax-web-scraper cat /proc/meminfo
```

### Log Management

```bash
# View real-time logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f truedax-scraper
docker-compose logs -f nginx

# Log rotation (add to crontab)
docker system prune -f --filter "until=24h"
```

## 🛠 Troubleshooting

### Common Issues

**1. Container fails to start**
```bash
# Check logs
docker-compose logs truedax-scraper

# Check resource usage
docker system df
free -h
```

**2. Chrome/JavaScript scraping fails**
```bash
# Check Chrome installation
docker exec truedax-web-scraper /usr/bin/chromium-browser --version

# Test Chrome flags
docker exec truedax-web-scraper /usr/bin/chromium-browser --headless --no-sandbox --dump-dom https://example.com
```

**3. Permission issues**
```bash
# Fix results directory permissions
sudo chown -R 1000:1000 data/results/
```

**4. Out of memory errors**
```bash
# Increase Docker memory limit
# In Docker Desktop: Settings > Resources > Memory

# Or reduce concurrent jobs
# Edit .env: MAX_CONCURRENT_JOBS=2
```

### Performance Tuning

**Memory Optimization:**
```yaml
# docker-compose.yml
deploy:
  resources:
    limits:
      memory: 1G
    reservations:
      memory: 256M
```

**Chrome Optimization:**
```bash
# Add to CHROMIUM_FLAGS in .env
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless --memory-pressure-off --disable-background-timer-throttling
```

## 🔄 Updates & Maintenance

### Updating the Application

```bash
# Stop services
docker-compose down

# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose up --build -d

# Check health
curl http://localhost:9000/health
```

### Backup Strategy

```bash
# Backup results data
tar -czf backup-$(date +%Y%m%d).tar.gz data/

# Restore from backup
tar -xzf backup-20231201.tar.gz
```

### Log Rotation

Add to crontab:
```bash
# Rotate logs daily at 2 AM
0 2 * * * cd /path/to/truedax && docker-compose logs --no-color > logs/app-$(date +\%Y\%m\%d).log 2>&1 && docker-compose restart
```

## 🚀 Cloud Deployment

### AWS ECS

```bash
# Build for ARM64 (Graviton)
docker buildx build --platform linux/arm64 -t truedax-scraper:latest .

# Push to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 123456789.dkr.ecr.us-west-2.amazonaws.com
docker tag truedax-scraper:latest 123456789.dkr.ecr.us-west-2.amazonaws.com/truedax-scraper:latest
docker push 123456789.dkr.ecr.us-west-2.amazonaws.com/truedax-scraper:latest
```

### Kubernetes

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: truedax-scraper
spec:
  replicas: 3
  selector:
    matchLabels:
      app: truedax-scraper
  template:
    metadata:
      labels:
        app: truedax-scraper
    spec:
      containers:
      - name: truedax-scraper
        image: truedax-scraper:latest
        ports:
        - containerPort: 9000
        resources:
          limits:
            memory: "2Gi"
            cpu: "1000m"
          requests:
            memory: "512Mi"
            cpu: "250m"
```

## 📈 Scaling

### Horizontal Scaling

```bash
# Scale to 3 instances
docker-compose up --scale truedax-scraper=3 -d

# Load balancer configuration needed for multiple instances
```

### Vertical Scaling

```yaml
# Increase resources in docker-compose.yml
deploy:
  resources:
    limits:
      cpus: '4.0'
      memory: 4G
```

## 🔐 Security Best Practices

1. **Network Security**
   - Use Docker networks for service isolation
   - Implement firewall rules
   - Regular security updates

2. **Container Security**
   - Run as non-root user
   - Use minimal base images
   - Regular vulnerability scanning

3. **Application Security**
   - Strong JWT secrets
   - Input validation
   - Rate limiting
   - HTTPS enforcement

4. **Data Security**
   - Encrypt data at rest
   - Secure backup storage
   - Access logging

## 📞 Support

For issues and questions:
- Check logs: `docker-compose logs -f`
- Health check: `curl http://localhost:9000/health`
- GitHub Issues: Create detailed issue with logs
- Documentation: See API docs at `/swagger/`
