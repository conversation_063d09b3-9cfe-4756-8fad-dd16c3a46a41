package config

import (
	"context"
	"fmt"
	"time"

	"github.com/joho/godotenv"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DatabaseConfig holds database-related configuration
type DatabaseConfig struct {
	URI         string
	Host        string
	Port        string
	Database    string
	Username    string
	Password    string
	MaxPoolSize int
	MinPoolSize int
	Timeout     time.Duration
}

// LoadDatabaseConfig loads database configuration from environment variables
func LoadDatabaseConfig() (*DatabaseConfig, error) {
	// Try to load .env file (ignore error if file doesn't exist)
	_ = godotenv.Load()

	config := &DatabaseConfig{
		URI:         getEnv("MONGODB_URI", ""),
		Host:        getEnv("MONGODB_HOST", "localhost"),
		Port:        getEnv("MONGODB_PORT", "27017"),
		Database:    getEnv("MONGODB_DATABASE", "truedax_scraper"),
		Username:    getEnv("MONGODB_USERNAME", "truedax_user"),
		Password:    getEnv("MONGODB_PASSWORD", "truedax_secure_password_2024"),
		MaxPoolSize: getEnvAsInt("MONGODB_MAX_POOL_SIZE", 100),
		MinPoolSize: getEnvAsInt("MONGODB_MIN_POOL_SIZE", 5),
		Timeout:     getEnvAsDuration("MONGODB_TIMEOUT", "10s"),
	}

	// Build MongoDB URI if not provided
	if config.URI == "" {
		config.URI = fmt.Sprintf(
			"mongodb://%s:%s@%s:%s/%s?authSource=admin",
			config.Username,
			config.Password,
			config.Host,
			config.Port,
			config.Database,
		)
	}

	return config, nil
}

var (
	Client   *mongo.Client
	Database *mongo.Database
)

// InitializeDatabase initializes the MongoDB connection
func InitializeDatabase(cfg *Config) error {
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Database.Timeout)
	defer cancel()

	// Set client options
	clientOptions := options.Client().ApplyURI(cfg.Database.URI)
	clientOptions.SetMaxPoolSize(uint64(cfg.Database.MaxPoolSize))
	clientOptions.SetMinPoolSize(uint64(cfg.Database.MinPoolSize))
	clientOptions.SetMaxConnIdleTime(30 * time.Second)

	// Connect to MongoDB
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// Test the connection
	err = client.Ping(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	Client = client
	Database = client.Database(cfg.Database.Database)

	return nil
}

// Close closes the MongoDB connection
func Close() error {
	if Client != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return Client.Disconnect(ctx)
	}
	return nil
}

// GetCollection returns a MongoDB collection
func GetCollection(name string) *mongo.Collection {
	return Database.Collection(name)
}

// HealthCheck checks if the database is healthy
func HealthCheck() error {
	if Client == nil {
		return fmt.Errorf("database not initialized")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return Client.Ping(ctx, nil)
}
