package scraper

import (
	"context"
	"time"

	"go-rest-api/internal/model"
)

// ScraperResult represents the result of scraping a single page
type ScraperResult struct {
	URL              string                 `json:"url"`
	Title            string                 `json:"title,omitempty"`
	Description      string                 `json:"description,omitempty"`
	StatusCode       int                    `json:"status_code"`
	Data             map[string]interface{} `json:"data"`
	Timestamp        time.Time              `json:"timestamp"`
	Success          bool                   `json:"success"`
	Error            string                 `json:"error,omitempty"`
	LoadTime         time.Duration          `json:"load_time"`
	ResponseTime     int64                  `json:"response_time_ms"`
	ContentLength    int                    `json:"content_length"`
	JavaScriptUsed   bool                   `json:"javascript_used"`
}

// ProgressCallback is called to report scraping progress
type ProgressCallback func(current, total int, message string)

// Scraper interface defines the contract for web scrapers
type Scraper interface {
	// Scrape performs the actual scraping operation
	Scrape(ctx context.Context, config model.ScrapingConfig, progressCallback ProgressCallback) ([]*ScraperResult, error)
	
	// ValidateConfig validates the scraping configuration
	ValidateConfig(config model.ScrapingConfig) error
	
	// EstimatePages estimates the number of pages to scrape
	EstimatePages(config model.ScrapingConfig) (int, error)
}

// ScraperOptions contains options for configuring the scraper
type ScraperOptions struct {
	MaxConcurrentRequests int           `json:"max_concurrent_requests"`
	RequestTimeout        time.Duration `json:"request_timeout"`
	RetryAttempts         int           `json:"retry_attempts"`
	RetryDelay           time.Duration `json:"retry_delay"`
	RespectRobotsTxt     bool          `json:"respect_robots_txt"`
	EnableCaching        bool          `json:"enable_caching"`
	CacheExpiry          time.Duration `json:"cache_expiry"`
	MaxMemoryUsage       int64         `json:"max_memory_usage"` // in bytes
}

// DefaultScraperOptions returns default scraper options
func DefaultScraperOptions() ScraperOptions {
	return ScraperOptions{
		MaxConcurrentRequests: 5,
		RequestTimeout:        30 * time.Second,
		RetryAttempts:         3,
		RetryDelay:           2 * time.Second,
		RespectRobotsTxt:     true,
		EnableCaching:        true,
		CacheExpiry:          1 * time.Hour,
		MaxMemoryUsage:       500 * 1024 * 1024, // 500MB
	}
}
