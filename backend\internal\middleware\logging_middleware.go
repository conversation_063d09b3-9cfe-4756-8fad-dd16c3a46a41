package middleware

import (
	"bytes"
	"io"
	"time"

	"go-rest-api/config"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// LoggingMiddleware provides request/response logging
type LoggingMiddleware struct{}

// NewLoggingMiddleware creates a new LoggingMiddleware
func NewLoggingMiddleware() *LoggingMiddleware {
	return &LoggingMiddleware{}
}

// RequestLogger middleware that logs HTTP requests and responses
func (m *LoggingMiddleware) RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate request ID
		requestID := uuid.New().String()
		c.Set("request_id", requestID)

		// Start time
		startTime := time.Now()

		// Read request body for logging (if needed)
		var requestBody []byte
		if c.Request.Body != nil && shouldLogRequestBody(c.Request.URL.Path, c.Request.Method) {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// Create response writer wrapper to capture response
		responseWriter := &responseWriter{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
		}
		c.Writer = responseWriter

		// Log request
		config.WithFields(logrus.Fields{
			"request_id":     requestID,
			"method":         c.Request.Method,
			"path":           c.Request.URL.Path,
			"query":          c.Request.URL.RawQuery,
			"ip":             c.ClientIP(),
			"user_agent":     c.GetHeader("User-Agent"),
			"content_length": c.Request.ContentLength,
			"type":           "request",
		}).Info("HTTP Request")

		// Process request
		c.Next()

		// Calculate duration
		duration := time.Since(startTime)

		// Get user info if available
		var userID interface{}
		var userEmail interface{}
		if uid, exists := c.Get("user_id"); exists {
			userID = uid
		}
		if email, exists := c.Get("email"); exists {
			userEmail = email
		}

		// Log response
		logFields := logrus.Fields{
			"request_id":      requestID,
			"method":          c.Request.Method,
			"path":            c.Request.URL.Path,
			"status_code":     c.Writer.Status(),
			"duration_ms":     duration.Milliseconds(),
			"response_size":   responseWriter.body.Len(),
			"ip":              c.ClientIP(),
			"type":            "response",
		}

		if userID != nil {
			logFields["user_id"] = userID
		}
		if userEmail != nil {
			logFields["user_email"] = userEmail
		}

		// Add error information if status code indicates an error
		if c.Writer.Status() >= 400 {
			logFields["response_body"] = responseWriter.body.String()
			if len(c.Errors) > 0 {
				logFields["errors"] = c.Errors.String()
			}
		}

		// Log with appropriate level based on status code
		logLevel := getLogLevelForStatus(c.Writer.Status())
		config.WithFields(logFields).Log(logLevel, "HTTP Response")

		// Log slow requests
		if duration > 1*time.Second {
			config.WithFields(logrus.Fields{
				"request_id":  requestID,
				"method":      c.Request.Method,
				"path":        c.Request.URL.Path,
				"duration_ms": duration.Milliseconds(),
				"type":        "slow_request",
			}).Warn("Slow HTTP Request")
		}
	}
}

// ErrorLogger middleware that logs errors
func (m *LoggingMiddleware) ErrorLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Log any errors that occurred during request processing
		for _, err := range c.Errors {
			requestID, _ := c.Get("request_id")

			config.WithFields(logrus.Fields{
				"request_id": requestID,
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"error":      err.Error(),
				"type":       "error",
			}).Error("Request Error")
		}
	}
}

// responseWriter wraps gin.ResponseWriter to capture response body
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// shouldLogRequestBody determines if request body should be logged
func shouldLogRequestBody(path, method string) bool {
	// Don't log request body for certain endpoints
	skipPaths := []string{
		"/health",
		"/swagger",
	}

	for _, skipPath := range skipPaths {
		if path == skipPath {
			return false
		}
	}

	// Only log body for POST, PUT, PATCH requests
	return method == "POST" || method == "PUT" || method == "PATCH"
}

// getLogLevelForStatus returns appropriate log level based on HTTP status code
func getLogLevelForStatus(statusCode int) logrus.Level {
	switch {
	case statusCode >= 500:
		return logrus.ErrorLevel
	case statusCode >= 400:
		return logrus.WarnLevel
	case statusCode >= 300:
		return logrus.InfoLevel
	default:
		return logrus.InfoLevel
	}
}
