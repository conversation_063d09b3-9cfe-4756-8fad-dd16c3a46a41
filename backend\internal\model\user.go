package model

import (
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserRole represents user roles in the system
type UserRole string

const (
	UserRoleUser  UserRole = "user"
	UserRoleAdmin UserRole = "admin"
)

// User represents a user in the system
type User struct {
	ID                   primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UUID                 uuid.UUID          `json:"uuid" bson:"uuid"`
	Email                string             `json:"email" bson:"email" binding:"required,email"`
	PasswordHash         string             `json:"-" bson:"password_hash"`
	Role                 UserRole           `json:"role" bson:"role"`
	FirstName            string             `json:"first_name" bson:"first_name"`
	LastName             string             `json:"last_name" bson:"last_name"`
	APIKey               string             `json:"api_key,omitempty" bson:"api_key,omitempty"`
	CreatedAt            time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt            time.Time          `json:"updated_at" bson:"updated_at"`
	LastLogin            *time.Time         `json:"last_login,omitempty" bson:"last_login,omitempty"`
	IsActive             bool               `json:"is_active" bson:"is_active"`
	EmailVerified        bool               `json:"email_verified" bson:"email_verified"`
	FailedLoginAttempts  int                `json:"-" bson:"failed_login_attempts"`
	LockedUntil          *time.Time         `json:"-" bson:"locked_until,omitempty"`
}

// RefreshToken represents a refresh token for stateless authentication
type RefreshToken struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UUID       uuid.UUID          `json:"uuid" bson:"uuid"`
	UserID     uuid.UUID          `json:"user_id" bson:"user_id"`
	TokenHash  string             `json:"-" bson:"token_hash"`
	ExpiresAt  time.Time          `json:"expires_at" bson:"expires_at"`
	CreatedAt  time.Time          `json:"created_at" bson:"created_at"`
	RevokedAt  *time.Time         `json:"revoked_at,omitempty" bson:"revoked_at,omitempty"`
	DeviceInfo string             `json:"device_info,omitempty" bson:"device_info,omitempty"`
	IPAddress  string             `json:"ip_address,omitempty" bson:"ip_address,omitempty"`
}

// BeforeInsert sets default values before inserting
func (u *User) BeforeInsert() {
	if u.UUID == uuid.Nil {
		u.UUID = uuid.New()
	}
	if u.CreatedAt.IsZero() {
		u.CreatedAt = time.Now().UTC()
	}
	if u.UpdatedAt.IsZero() {
		u.UpdatedAt = time.Now().UTC()
	}
	if u.Role == "" {
		u.Role = UserRoleUser
	}
	if !u.IsActive {
		u.IsActive = true
	}
}

// BeforeInsert sets default values before inserting
func (rt *RefreshToken) BeforeInsert() {
	if rt.UUID == uuid.Nil {
		rt.UUID = uuid.New()
	}
	if rt.CreatedAt.IsZero() {
		rt.CreatedAt = time.Now().UTC()
	}
}

// IsLocked checks if the user account is currently locked
func (u *User) IsLocked() bool {
	return u.LockedUntil != nil && u.LockedUntil.After(time.Now())
}

// FullName returns the user's full name
func (u *User) FullName() string {
	if u.FirstName == "" && u.LastName == "" {
		return u.Email
	}
	return u.FirstName + " " + u.LastName
}

// IsExpired checks if the refresh token is expired
func (rt *RefreshToken) IsExpired() bool {
	return time.Now().After(rt.ExpiresAt)
}

// IsRevoked checks if the refresh token is revoked
func (rt *RefreshToken) IsRevoked() bool {
	return rt.RevokedAt != nil
}
