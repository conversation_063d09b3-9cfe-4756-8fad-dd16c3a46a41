package handler

import (
	"net/http"

	"go-rest-api/internal/services/monitoring"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	monitoringService *monitoring.MonitoringService
}

// NewHealthHandler creates a new HealthHandler
func NewHealthHandler(monitoringService *monitoring.MonitoringService) *HealthHandler {
	return &HealthHandler{
		monitoringService: monitoringService,
	}
}

// HealthCheck godoc
// @Summary      Health check endpoint
// @Description  Returns the health status of the application
// @Tags         health
// @Accept       json
// @Produce      json
// @Success      200 {object} monitoring.HealthStatus
// @Failure      503 {object} monitoring.HealthStatus
// @Router       /health [get]
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	healthStatus := h.monitoringService.GetHealthStatus()

	statusCode := http.StatusOK
	if healthStatus.Status != "healthy" {
		statusCode = http.StatusServiceUnavailable
	}

	c.<PERSON><PERSON><PERSON>(statusCode, healthStatus)
}


