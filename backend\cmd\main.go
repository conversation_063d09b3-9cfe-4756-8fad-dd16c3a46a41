package main

// @title           TrueDax Web Scraper API
// @version         1.0
// @description     A REST API for web scraping operations
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:9000
// @BasePath  /

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

import (
	"log"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/repository/mongodb"
	"go-rest-api/internal/router"
	"go-rest-api/internal/services/monitoring"

	_ "go-rest-api/docs"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	config.Initialize(cfg)

	// Initialize database
	if err := config.InitializeDatabase(cfg); err != nil {
		config.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize repositories
	repos := mongodb.NewRepositories()

	// Initialize monitoring service and start metrics collection
	monitoringService := monitoring.NewMonitoringService(repos)
	monitoringService.StartMetricsCollection(5 * time.Minute)

	// Log system startup
	monitoringService.LogSystemEvent("info", "startup", "Application starting", map[string]interface{}{
		"version": "1.0.0",
		"port":    cfg.Server.Port,
	})

	// Setup router with dependencies
	r := router.SetupRouter(cfg, repos)

	config.Infof("Server starting on port %s", cfg.Server.Port)
	config.Fatal(r.Run(":" + cfg.Server.Port))
}