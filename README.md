# TrueDax Web Scraper

A professional, scalable web scraping platform built with Go, featuring async job scheduling, comprehensive content extraction, and robust database storage.

## 🚀 Features

### Core Functionality
- **Async Job Scheduling**: Priority-based job queue with concurrent processing
- **Comprehensive Web Scraping**: HTTP and headless Chrome (chromedp) support
- **Database-Based Storage**: MongoDB integration for scalable result storage
- **Content Extraction**: Headings, paragraphs, links, images, tables, forms, and structured data
- **Authentication & Authorization**: JWT-based stateless auth with role-based access (user/admin)
- **Audit Logging**: Comprehensive activity tracking and monitoring
- **Health Monitoring**: System metrics and health checks
- **Docker Support**: Containerized deployment with multi-stage builds

### Technical Stack
- **Backend**: Go 1.21+ with Gin framework
- **Database**: MongoDB with Docker deployment
- **Authentication**: JWT tokens (access/refresh pattern)
- **Web Scraping**: HTTP client + chromedp for JavaScript-heavy sites
- **Containerization**: Docker with Nginx reverse proxy
- **Architecture**: Clean architecture with repository pattern

## 📁 Project Structure

```
├── backend/
│   ├── cmd/
│   │   └── main.go                 # Application entry point
│   ├── config/                    # Configuration files (all at same level)
│   │   ├── app.go                 # Application configuration
│   │   ├── database.go            # Database configuration
│   │   └── logger.go              # Logger configuration
│   ├── internal/
│   │   ├── services/              # Business logic services (unified)
│   │   │   ├── auth/              # Authentication service
│   │   │   ├── scraper/           # Web scraping services
│   │   │   ├── job/               # Job management service
│   │   │   ├── audit/             # Audit logging service
│   │   │   └── monitoring/        # System monitoring service
│   │   ├── handler/               # HTTP request handlers
│   │   ├── middleware/            # HTTP middleware
│   │   ├── model/                 # Data models
│   │   ├── repository/            # Data access layer
│   │   └── router/                # Route definitions
│   ├── docs/                      # API documentation (Swagger)
│   ├── go.mod                     # Go module definition
│   └── go.sum                     # Go module checksums
├── docker-compose.yml             # Docker services configuration
├── Dockerfile                     # Application container
├── nginx/                         # Reverse proxy configuration
├── scripts/                       # Deployment and utility scripts
└── README.md                      # This file
```

## 🛠️ Installation & Setup

### Prerequisites
- Go 1.21+
- Docker & Docker Compose
- MongoDB (via Docker)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tdwebscraper
   ```

2. **Environment Configuration**
   Create `.env` file:
   ```env
   # Database
   MONGO_URI=mongodb://localhost:27017
   MONGO_DATABASE=webscraper

   # JWT
   JWT_SECRET=your-super-secret-jwt-key
   JWT_ACCESS_EXPIRY=15m
   JWT_REFRESH_EXPIRY=7d

   # Server
   SERVER_PORT=8080
   SERVER_HOST=localhost

   # Admin
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=admin123
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Manual Setup (Development)**
   ```bash
   # Start MongoDB
   docker run -d -p 27017:27017 --name mongodb mongo:latest

   # Install dependencies
   cd backend
   go mod download

   # Run the application
   go run cmd/main.go
   ```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - User logout

### Job Management Endpoints
- `POST /api/jobs` - Create scraping job
- `GET /api/jobs` - List user jobs
- `GET /api/jobs/{id}` - Get job details
- `GET /api/jobs/{id}/results` - Get job results (paginated)
- `DELETE /api/jobs/{id}` - Cancel/delete job
- `POST /api/jobs/test-scrape` - Test single URL scraping

### Admin Endpoints
- `GET /api/admin/users` - List all users
- `GET /api/admin/jobs` - List all jobs
- `GET /api/admin/system/health` - System health check

### Health & Monitoring
- `GET /health` - Basic health check
- `GET /api/health/detailed` - Detailed system status

## 🔧 Configuration

### Application Configuration (`config/app.go`)
- Server settings (host, port, timeouts)
- JWT configuration (secrets, expiry times)
- Admin user setup
- CORS and security settings

### Database Configuration (`config/database.go`)
- MongoDB connection settings
- Connection pooling
- Timeout configurations

## 🗄️ Database Schema

### Collections

#### `users`
- User accounts with email/password authentication
- Role-based access control (user/admin)
- Account creation and last login tracking

#### `refresh_tokens`
- JWT refresh token management
- Token expiry and revocation

#### `jobs`
- Scraping job definitions and status
- User association and priority settings
- Progress tracking and result metadata

#### `scraping_results`
- Comprehensive scraped content storage
- Structured data extraction results
- Metadata and performance metrics

#### `audit_logs`
- User activity tracking
- System event logging
- Security and compliance monitoring

## 🚀 Deployment

### Docker Deployment
```bash
# Build and deploy
docker-compose up -d

# Scale workers
docker-compose up -d --scale backend=3

# View logs
docker-compose logs -f backend
```

### Production Considerations
- Use environment-specific configuration files
- Set up proper MongoDB replica sets
- Configure Nginx for SSL termination
- Implement log aggregation (ELK stack)
- Set up monitoring and alerting
- Use secrets management for sensitive data

## 🔍 Web Scraping Capabilities

### Content Extraction
- **Text Content**: Headings (H1-H6), paragraphs, main content
- **Links**: URLs, anchor text, titles, targets
- **Images**: URLs, alt text, dimensions
- **Tables**: Headers and row data
- **Forms**: Fields, types, validation rules
- **Lists**: Ordered and unordered list items
- **Structured Data**: JSON-LD, microdata, RDFa

### Scraping Methods
- **HTTP Client**: Fast, lightweight for static content
- **Headless Chrome**: JavaScript execution for dynamic content
- **Smart Detection**: Automatic method selection based on content type

### Performance Features
- Concurrent job processing
- Configurable delays and timeouts
- User-agent rotation
- Error handling and retry logic
- Progress tracking and callbacks

## 🛡️ Security Features

- **Authentication**: JWT-based stateless authentication
- **Authorization**: Role-based access control
- **Audit Logging**: Comprehensive activity tracking
- **Input Validation**: Request sanitization and validation
- **Rate Limiting**: API endpoint protection
- **CORS**: Cross-origin request handling

## 📊 Monitoring & Logging

### System Monitoring
- Health check endpoints
- Performance metrics collection
- Resource usage tracking
- Database connection monitoring

### Audit Logging
- User authentication events
- Job creation and execution
- Admin actions
- System errors and warnings

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/docs` endpoint
- Review the logs for troubleshooting

---

**TrueDax Web Scraper** - Professional web scraping made simple and scalable.
- **Resource limits** for controlled resource usage

## 📋 Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│     Nginx       │────│  TrueDax API    │────│   File System  │
│   (Optional)    │    │   (Go Service)  │    │   (Results)     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
   Port 80/443              Port 9000              Volume Mount
```

## 🔧 Configuration

All configuration is handled through environment variables. Copy `.env.example` to `.env` and customize:

```bash
# Core Configuration
PORT=9000
MAX_CONCURRENT_JOBS=5

# Chrome/JavaScript Support  
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --headless

# Data Persistence
RESULTS_DIRECTORY=/app/results
CLEANUP_INTERVAL=24h
```

## 📖 Documentation

- **[Docker Deployment Guide](DOCKER-DEPLOYMENT.md)** - Complete Docker setup guide
- **[API Documentation](backend/internal/README.md)** - Detailed API reference
- **[Swagger UI](http://localhost:9000/swagger/)** - Interactive API explorer (when running)