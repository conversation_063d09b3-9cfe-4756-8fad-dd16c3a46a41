# TrueDax Web Scraper Environment Configuration
# Copy this file to .env and customize the values

# Server Configuration
PORT=9000
GIN_MODE=release

# Database Configuration
DATABASE_URL=postgresql://truedax_user:truedax_secure_password_2024@localhost:5432/truedax_scraper?sslmode=disable
DB_HOST=localhost
DB_PORT=5432
DB_NAME=truedax_scraper
DB_USER=truedax_user
DB_PASSWORD=truedax_secure_password_2024
DB_SSLMODE=disable
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=300s

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-in-production-2024
JWT_ACCESS_TOKEN_DURATION=15m
JWT_REFRESH_TOKEN_DURATION=7d

# Scraper Configuration
MAX_CONCURRENT_JOBS=5
RESULTS_DIRECTORY=/app/results
CLEANUP_INTERVAL=24h
MAX_RESULT_AGE=720h

# Chrome Configuration for JavaScript scraping
CHROME_BIN=/usr/bin/chromium-browser
CHROME_PATH=/usr/bin/chromium-browser
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless

# Rate Limiting
REQUESTS_PER_MINUTE=60
BURST_SIZE=10

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# Security Configuration
BCRYPT_COST=12
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=30m

# Redis (for job queue optimization - optional)
# REDIS_URL=redis://localhost:6379/0
