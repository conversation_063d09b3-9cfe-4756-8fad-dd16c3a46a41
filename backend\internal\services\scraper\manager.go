package scraper

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go-rest-api/internal/model"
	"go-rest-api/internal/repository"
)

// ScraperManager manages different types of scrapers and handles result storage
type ScraperManager struct {
	scrapers     map[string]Scraper
	resultsDir   string
	options      ScraperOptions
	repos        *repository.Repositories
}

// NewScraperManager creates a new scraper manager
func NewScraperManager(resultsDir string, options ScraperOptions, repos *repository.Repositories) *ScraperManager {
	// Ensure results directory exists (for backward compatibility)
	os.MkdirAll(resultsDir, 0755)

	manager := &ScraperManager{
		scrapers:   make(map[string]Scraper),
		resultsDir: resultsDir,
		options:    options,
		repos:      repos,
	}

	// Register default scrapers
	manager.RegisterScraper("web", NewWebScraper(options))

	return manager
}

// RegisterScraper registers a new scraper type
func (sm *ScraperManager) RegisterScraper(name string, scraper Scraper) {
	sm.scrapers[name] = scraper
}

// GetScraper returns a scraper by name
func (sm *ScraperManager) GetScraper(name string) (Scraper, error) {
	scraper, exists := sm.scrapers[name]
	if !exists {
		return nil, fmt.Errorf("scraper '%s' not found", name)
	}
	return scraper, nil
}

// ExecuteJob executes a scraping job and stores the results
func (sm *ScraperManager) ExecuteJob(ctx context.Context, job *model.Job, progressCallback ProgressCallback) error {
	// Get the appropriate scraper (for now, we only have web scraper)
	scraper, err := sm.GetScraper("web")
	if err != nil {
		return fmt.Errorf("failed to get scraper: %w", err)
	}
	
	// Validate configuration
	if err := scraper.ValidateConfig(job.Config); err != nil {
		return fmt.Errorf("invalid scraping configuration: %w", err)
	}
	
	// Start scraping
	startTime := time.Now()
	results, err := scraper.Scrape(ctx, job.Config, progressCallback)
	if err != nil {
		return fmt.Errorf("scraping failed: %w", err)
	}
	
	// Store results to database
	err = sm.storeResultsToDatabase(ctx, job.JobID, results)
	if err != nil {
		return fmt.Errorf("failed to store results to database: %w", err)
	}

	// Update job with results
	job.ResultCount = len(results)
	job.ResultURL = fmt.Sprintf("/api/jobs/%s/results", job.JobID) // API endpoint for results
	
	// Calculate success rate
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}
	
	if len(results) > 0 {
		successRate := float64(successCount) / float64(len(results)) * 100
		if successRate < 50 {
			return fmt.Errorf("scraping success rate too low: %.1f%%", successRate)
		}
	}
	
	duration := time.Since(startTime)
	fmt.Printf("Scraping completed in %v. Success rate: %.1f%% (%d/%d)\n",
		duration, float64(successCount)/float64(len(results))*100, successCount, len(results))
	fmt.Printf("Results saved to database for job: %s\n", job.JobID)
	
	return nil
}

// storeResultsToDatabase stores scraping results to the database
func (sm *ScraperManager) storeResultsToDatabase(ctx context.Context, jobID string, results []*ScraperResult) error {
	for _, result := range results {
		// Convert ScraperResult to ScrapingResult model
		scrapingResult := sm.convertToScrapingResult(jobID, result)

		// Store in database
		if err := sm.repos.Scraper.CreateScrapingResult(ctx, scrapingResult); err != nil {
			return fmt.Errorf("failed to store scraping result for URL %s: %w", result.URL, err)
		}
	}

	return nil
}

// convertToScrapingResult converts a ScraperResult to a ScrapingResult model
func (sm *ScraperManager) convertToScrapingResult(jobID string, result *ScraperResult) *model.ScrapingResult {
	scrapingResult := &model.ScrapingResult{
		JobID:       jobID,
		URL:         result.URL,
		Title:       result.Title,
		Description: result.Description,
		StatusCode:  result.StatusCode,
		Success:     result.Success,
		ErrorMsg:    result.Error,
		ScrapedAt:   result.Timestamp,
	}

	// Convert content data
	if result.Data != nil {
		content := model.ScrapingContent{}

		// Extract content from the data map
		if contentMap, ok := result.Data["content"].(map[string]interface{}); ok {
			// Convert headings
			if headings, ok := contentMap["headings"].([]interface{}); ok {
				for _, h := range headings {
					if hMap, ok := h.(map[string]interface{}); ok {
						heading := model.HeadingData{}
						if level, ok := hMap["level"].(float64); ok {
							heading.Level = int(level)
						}
						if text, ok := hMap["text"].(string); ok {
							heading.Text = text
						}
						content.Headings = append(content.Headings, heading)
					}
				}
			}

			// Convert paragraphs
			if paragraphs, ok := contentMap["paragraphs"].([]interface{}); ok {
				for _, p := range paragraphs {
					if pStr, ok := p.(string); ok {
						content.Paragraphs = append(content.Paragraphs, pStr)
					}
				}
			}

			// Convert main content and all text
			if mainContent, ok := contentMap["main_content"].(string); ok {
				content.MainContent = mainContent
			}
			if allText, ok := contentMap["all_text"].(string); ok {
				content.AllText = allText
			}

			// Store structured data as-is
			if structuredData, ok := contentMap["structured_data"]; ok {
				content.StructuredData = structuredData
			}
		}

		scrapingResult.Content = content
	}

	// Set metadata
	scrapingResult.Metadata = model.ScrapingMetadata{
		ResponseTime:   result.ResponseTime,
		ContentLength:  int64(result.ContentLength),
		JavaScriptUsed: result.JavaScriptUsed,
	}

	return scrapingResult
}

// calculateSuccessRate calculates the success rate of scraping results
func (sm *ScraperManager) calculateSuccessRate(results []*ScraperResult) float64 {
	if len(results) == 0 {
		return 0.0
	}
	
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}
	
	return float64(successCount) / float64(len(results)) * 100
}

// GetResults retrieves stored results for a job from database
func (sm *ScraperManager) GetResults(ctx context.Context, jobID string, limit, offset int) (*ScrapingResults, error) {
	// Get scraping results from database
	results, err := sm.repos.Scraper.GetScrapingResultsByJobID(ctx, jobID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get results from database: %w", err)
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("no results found for job %s", jobID)
	}

	// Convert to legacy format for compatibility
	scraperResults := make([]*ScraperResult, len(results))
	for i, result := range results {
		scraperResults[i] = sm.convertFromScrapingResult(result)
	}

	// Create results summary
	summary := &ScrapingResults{
		JobID:       jobID,
		Timestamp:   time.Now(),
		TotalPages:  len(scraperResults),
		SuccessRate: sm.calculateSuccessRate(scraperResults),
		Results:     scraperResults,
	}

	return summary, nil
}

// convertFromScrapingResult converts a ScrapingResult model back to ScraperResult
func (sm *ScraperManager) convertFromScrapingResult(result *model.ScrapingResult) *ScraperResult {
	scraperResult := &ScraperResult{
		URL:              result.URL,
		Title:            result.Title,
		Description:      result.Description,
		StatusCode:       result.StatusCode,
		Success:          result.Success,
		Error:            result.ErrorMsg,
		Timestamp:        result.ScrapedAt,
		ResponseTime:     result.Metadata.ResponseTime,
		ContentLength:    int(result.Metadata.ContentLength),
		JavaScriptUsed:   result.Metadata.JavaScriptUsed,
	}

	// Convert content back to data map
	data := make(map[string]interface{})
	content := make(map[string]interface{})

	// Convert headings
	if len(result.Content.Headings) > 0 {
		headings := make([]map[string]interface{}, len(result.Content.Headings))
		for i, h := range result.Content.Headings {
			headings[i] = map[string]interface{}{
				"level": h.Level,
				"text":  h.Text,
			}
		}
		content["headings"] = headings
	}

	// Convert paragraphs
	if len(result.Content.Paragraphs) > 0 {
		content["paragraphs"] = result.Content.Paragraphs
	}

	// Add other content
	if result.Content.MainContent != "" {
		content["main_content"] = result.Content.MainContent
	}
	if result.Content.AllText != "" {
		content["all_text"] = result.Content.AllText
	}
	if result.Content.StructuredData != nil {
		content["structured_data"] = result.Content.StructuredData
	}

	data["title"] = result.Title
	data["description"] = result.Description
	data["content"] = content

	scraperResult.Data = data

	return scraperResult
}

// CleanupOldResults removes old results from database and optionally old files
func (sm *ScraperManager) CleanupOldResults(ctx context.Context, maxAge time.Duration) error {
	// Clean up database results
	if err := sm.repos.Scraper.DeleteOldScrapingResults(ctx, maxAge); err != nil {
		return fmt.Errorf("failed to cleanup old database results: %w", err)
	}

	// Also clean up old scraped pages
	if err := sm.repos.Scraper.DeleteOldScrapedPages(ctx, maxAge); err != nil {
		return fmt.Errorf("failed to cleanup old scraped pages: %w", err)
	}

	// Clean up old result files (for backward compatibility)
	files, err := filepath.Glob(filepath.Join(sm.resultsDir, "*.json"))
	if err != nil {
		return fmt.Errorf("failed to list result files: %w", err)
	}

	cutoff := time.Now().Add(-maxAge)
	deletedCount := 0

	for _, file := range files {
		info, err := os.Stat(file)
		if err != nil {
			continue // Skip files we can't stat
		}

		if info.ModTime().Before(cutoff) {
			if err := os.Remove(file); err != nil {
				fmt.Printf("Failed to remove old result file %s: %v\n", file, err)
			} else {
				deletedCount++
			}
		}
	}

	if deletedCount > 0 {
		fmt.Printf("Cleaned up %d old result files\n", deletedCount)
	}

	return nil
}

// ScrapingResults represents the complete results of a scraping job
type ScrapingResults struct {
	JobID       string           `json:"job_id"`
	Timestamp   time.Time        `json:"timestamp"`
	TotalPages  int              `json:"total_pages"`
	SuccessRate float64          `json:"success_rate"`
	Results     []*ScraperResult `json:"results"`
	FilePath    string           `json:"file_path,omitempty"`
}
