package job

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"go-rest-api/internal/model"
	"go-rest-api/internal/repository"
	"go-rest-api/internal/services/scraper"
	"github.com/google/uuid"
)

// JobService handles business logic for job management
type JobService struct {
	jobs           map[string]*model.Job
	queue          []*model.Job
	mutex          sync.RWMutex
	worker         *JobWorker
	scraperManager *scraper.ScraperManager
	repos          *repository.Repositories
}

// JobWorker manages the execution of jobs
type JobWorker struct {
	maxConcurrentJobs int
	runningJobs       map[string]bool
	mutex             sync.RWMutex
	jobService        *JobService
}

// NewJobService creates a new job service instance
func NewJobService(repos *repository.Repositories) *JobService {
	// Initialize scraper manager with results directory and repositories
	scraperOptions := scraper.DefaultScraperOptions()
	scraperManager := scraper.NewScraperManager("./results", scraperOptions, repos)

	js := &JobService{
		jobs:           make(map[string]*model.Job),
		queue:          make([]*model.Job, 0),
		scraperManager: scraperManager,
		repos:          repos,
	}
	js.worker = &JobWorker{
		maxConcurrentJobs: 5, // Configurable based on cloud resources
		runningJobs:       make(map[string]bool),
		jobService:        js,
	}
	
	// Start the job processor
	go js.worker.processJobs()
	
	// Start cleanup routine for old results (runs every 24 hours)
	go js.startCleanupRoutine()
	
	return js
}

// CreateJob creates a new scraping job
func (js *JobService) CreateJob(userEmail string, req model.CreateJobRequest) (*model.Job, error) {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	// Validate the request
	if err := js.validateJobRequest(req); err != nil {
		return nil, err
	}

	job := &model.Job{
		JobID:       generateJobID(),
		UserEmail:   userEmail,
		Name:        req.Name,
		Description: req.Description,
		Config:      req.Config,
		Status:      model.JobStatusPending,
		Priority:    req.Priority,
		Progress:    0,
		CreatedAt:   time.Now().UTC(),
		ResultCount: 0,
	}

	js.jobs[job.JobID] = job
	js.addToQueue(job)

	return job, nil
}

// GetJob retrieves a job by ID
func (js *JobService) GetJob(jobID, userEmail string) (*model.Job, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return nil, fmt.Errorf("unauthorized access to job")
	}

	return job, nil
}

// GetUserJobs retrieves all jobs for a user with pagination
func (js *JobService) GetUserJobs(userEmail string, offset, limit int) ([]*model.JobSummary, int, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	var userJobs []*model.Job
	for _, job := range js.jobs {
		if job.UserEmail == userEmail {
			userJobs = append(userJobs, job)
		}
	}

	// Sort by creation time (newest first)
	sort.Slice(userJobs, func(i, j int) bool {
		return userJobs[i].CreatedAt.After(userJobs[j].CreatedAt)
	})

	total := len(userJobs)
	
	// Apply pagination
	start := offset
	if start > total {
		start = total
	}
	
	end := start + limit
	if end > total {
		end = total
	}

	// Convert to summaries
	summaries := make([]*model.JobSummary, 0, end-start)
	for i := start; i < end; i++ {
		job := userJobs[i]
		summaries = append(summaries, &model.JobSummary{
			ID:          job.JobID,
			Name:        job.Name,
			Status:      job.Status,
			Priority:    job.Priority,
			Progress:    job.Progress,
			CreatedAt:   job.CreatedAt,
			ResultCount: job.ResultCount,
		})
	}

	return summaries, total, nil
}

// CancelJob cancels a pending or running job
func (js *JobService) CancelJob(jobID, userEmail string) error {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return fmt.Errorf("unauthorized access to job")
	}

	if job.Status == model.JobStatusCompleted || job.Status == model.JobStatusFailed {
		return fmt.Errorf("cannot cancel completed or failed job")
	}

	if job.Status == model.JobStatusCancelled {
		return fmt.Errorf("job is already cancelled")
	}

	job.Status = model.JobStatusCancelled
	js.removeFromQueue(jobID)

	return nil
}

// DeleteJob deletes a job and its results
func (js *JobService) DeleteJob(jobID, userEmail string) error {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return fmt.Errorf("unauthorized access to job")
	}

	// Cancel if running
	if job.Status == model.JobStatusRunning || job.Status == model.JobStatusPending {
		job.Status = model.JobStatusCancelled
		js.removeFromQueue(jobID)
	}

	// Remove from jobs map
	delete(js.jobs, jobID)

	return nil
}

// GetJobStatus returns the current status of a job
func (js *JobService) GetJobStatus(jobID, userEmail string) (*model.JobStatus, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return nil, fmt.Errorf("unauthorized access to job")
	}

	return &job.Status, nil
}

// GetJobProgress returns the current progress of a job
func (js *JobService) GetJobProgress(jobID, userEmail string) (int, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return 0, fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return 0, fmt.Errorf("unauthorized access to job")
	}

	return job.Progress, nil
}

// EstimateJobDuration estimates how long a job will take
func (js *JobService) EstimateJobDuration(config model.ScrapingConfig) (time.Duration, error) {
	scraper, err := js.scraperManager.GetScraper("web")
	if err != nil {
		return 0, err
	}

	pages, err := scraper.EstimatePages(config)
	if err != nil {
		return 0, err
	}

	// Rough estimation: 2 seconds per page + delay
	baseTime := time.Duration(pages) * 2 * time.Second
	delayTime := time.Duration(pages) * time.Duration(config.DelayMs) * time.Millisecond
	
	return baseTime + delayTime, nil
}

// GetJobResults retrieves the results for a completed job
func (js *JobService) GetJobResults(ctx context.Context, jobID, userEmail string, limit, offset int) (*scraper.ScrapingResults, error) {
	js.mutex.RLock()
	job, exists := js.jobs[jobID]
	js.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return nil, fmt.Errorf("unauthorized access to job")
	}

	if job.Status != model.JobStatusCompleted {
		return nil, fmt.Errorf("job is not completed yet")
	}

	return js.scraperManager.GetResults(ctx, jobID, limit, offset)
}

// validateJobRequest validates a job creation request
func (js *JobService) validateJobRequest(req model.CreateJobRequest) error {
	if req.Name == "" {
		return fmt.Errorf("job name is required")
	}

	if len(req.Name) > 100 {
		return fmt.Errorf("job name cannot exceed 100 characters")
	}

	if len(req.Description) > 500 {
		return fmt.Errorf("job description cannot exceed 500 characters")
	}

	if req.Priority < model.PriorityLow || req.Priority > model.PriorityCritical {
		return fmt.Errorf("invalid priority level")
	}

	// Validate scraping config
	if req.Config.URL == "" {
		return fmt.Errorf("URL is required")
	}

	if req.Config.MaxPages < 1 || req.Config.MaxPages > 1000 {
		return fmt.Errorf("max pages must be between 1 and 1000")
	}

	if req.Config.DelayMs < 0 || req.Config.DelayMs > 10000 {
		return fmt.Errorf("delay must be between 0 and 10000 milliseconds")
	}

	if req.Config.Timeout < 1 || req.Config.Timeout > 300 {
		return fmt.Errorf("timeout must be between 1 and 300 seconds")
	}

	return nil
}

// addToQueue adds a job to the processing queue
func (js *JobService) addToQueue(job *model.Job) {
	// Insert job in priority order (higher priority first)
	inserted := false
	for i, queuedJob := range js.queue {
		if job.Priority > queuedJob.Priority {
			// Insert at position i
			js.queue = append(js.queue[:i], append([]*model.Job{job}, js.queue[i:]...)...)
			inserted = true
			break
		}
	}

	if !inserted {
		js.queue = append(js.queue, job)
	}

	job.Status = model.JobStatusQueued
}

// removeFromQueue removes a job from the processing queue
func (js *JobService) removeFromQueue(jobID string) {
	for i, job := range js.queue {
		if job.JobID == jobID {
			js.queue = append(js.queue[:i], js.queue[i+1:]...)
			break
		}
	}
}

// startCleanupRoutine starts a background routine to clean up old result files
func (js *JobService) startCleanupRoutine() {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		// Clean up results older than 30 days
		if err := js.scraperManager.CleanupOldResults(context.Background(), 30 * 24 * time.Hour); err != nil {
			fmt.Printf("Error cleaning up old results: %v\n", err)
		}
	}
}

func generateJobID() string {
	return "job_" + uuid.New().String()[:8]
}

// UpdateJob updates an existing job
func (js *JobService) UpdateJob(jobID, userEmail string, req model.UpdateJobRequest) (*model.Job, error) {
	// Get the existing job
	job, err := js.GetJob(jobID, userEmail)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.Name != nil && *req.Name != "" {
		job.Name = *req.Name
	}
	if req.Description != nil && *req.Description != "" {
		job.Description = *req.Description
	}
	if req.Priority != nil {
		job.Priority = *req.Priority
	}

	// Update in repository - use existing method
	if err := js.repos.Job.UpdateStatus(context.Background(), job.JobID, job.Status); err != nil {
		return nil, fmt.Errorf("failed to update job: %w", err)
	}

	return job, nil
}

// GetJobsByUser retrieves all jobs for a specific user
func (js *JobService) GetJobsByUser(userEmail string, limit, offset int) ([]*model.Job, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	var userJobs []*model.Job
	for _, job := range js.jobs {
		if job.UserEmail == userEmail {
			userJobs = append(userJobs, job)
		}
	}

	// Apply pagination
	start := offset
	if start > len(userJobs) {
		return []*model.Job{}, nil
	}

	end := start + limit
	if end > len(userJobs) {
		end = len(userJobs)
	}

	return userJobs[start:end], nil
}

// GetJobStats returns statistics about jobs
func (js *JobService) GetJobStats(userEmail string) (*JobStats, error) {
	// Get user's jobs
	jobs, err := js.GetJobsByUser(userEmail, 1000, 0) // Get a large number to count all
	if err != nil {
		return nil, err
	}

	stats := &JobStats{}
	for _, job := range jobs {
		switch job.Status {
		case model.JobStatusPending:
			stats.Pending++
		case model.JobStatusRunning:
			stats.Running++
		case model.JobStatusCompleted:
			stats.Completed++
		case model.JobStatusFailed:
			stats.Failed++
		case model.JobStatusCancelled:
			stats.Cancelled++
		}
		stats.Total++
	}

	return stats, nil
}

// GetQueuePosition returns the position of a job in the queue
func (js *JobService) GetQueuePosition(jobID string) (int, error) {
	// For now, return a simple implementation
	// In a real implementation, this would check the actual queue position
	return 1, nil
}

// TestScrapeURL performs a test scrape of a single URL
func (js *JobService) TestScrapeURL(req TestScrapeRequest) (*TestScrapeResponse, error) {
	// Create a temporary job for testing
	testJobID := "test_" + generateJobID()
	testJob := &model.Job{
		JobID:       testJobID,
		UserEmail:   "<EMAIL>",
		Name:        "Test Scrape",
		Description: "Test scraping of single URL",
		Config: model.ScrapingConfig{
			URL:     req.URL,
			Timeout: 30,
		},
		Status:    model.JobStatusPending,
		Priority:  model.PriorityNormal,
		Progress:  0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Execute the test job
	err := js.scraperManager.ExecuteJob(context.Background(), testJob, nil)
	if err != nil {
		return &TestScrapeResponse{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// Get the results
	results, err := js.scraperManager.GetResults(context.Background(), testJob.JobID, 1, 0)
	if err != nil {
		return &TestScrapeResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get results: %v", err),
		}, nil
	}

	if len(results.Results) == 0 {
		return &TestScrapeResponse{
			Success: false,
			Error:   "No results returned",
		}, nil
	}

	return &TestScrapeResponse{
		Success: true,
		Data:    results.Results[0].Data,
	}, nil
}

// Request/Response types for the new methods

type JobStats struct {
	Total     int `json:"total"`
	Pending   int `json:"pending"`
	Running   int `json:"running"`
	Completed int `json:"completed"`
	Failed    int `json:"failed"`
	Cancelled int `json:"cancelled"`
}

type TestScrapeRequest struct {
	URL string `json:"url"`
}

type TestScrapeResponse struct {
	Success bool                   `json:"success"`
	Data    map[string]interface{} `json:"data,omitempty"`
	Error   string                 `json:"error,omitempty"`
}

// Job Worker methods

func (jw *JobWorker) processJobs() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		jw.checkAndStartJobs()
	}
}

func (jw *JobWorker) checkAndStartJobs() {
	jw.jobService.mutex.Lock()
	defer jw.jobService.mutex.Unlock()

	// Check how many jobs are currently running
	jw.mutex.RLock()
	runningCount := len(jw.runningJobs)
	jw.mutex.RUnlock()

	if runningCount >= jw.maxConcurrentJobs {
		return // Already at capacity
	}

	// Find next job to execute
	for i, job := range jw.jobService.queue {
		if job.Status == model.JobStatusQueued {
			// Remove from queue and start execution
			jw.jobService.queue = append(jw.jobService.queue[:i], jw.jobService.queue[i+1:]...)
			go jw.executeJob(job)
			break
		}
	}
}

func (jw *JobWorker) executeJob(job *model.Job) {
	jw.mutex.Lock()
	jw.runningJobs[job.JobID] = true
	jw.mutex.Unlock()

	defer func() {
		jw.mutex.Lock()
		delete(jw.runningJobs, job.JobID)
		jw.mutex.Unlock()
	}()

	// Update job status
	jw.jobService.mutex.Lock()
	job.Status = model.JobStatusRunning
	startTime := time.Now().UTC()
	job.StartedAt = &startTime
	jw.jobService.mutex.Unlock()

	// Create context for the job execution
	ctx := context.Background()

	// Create progress callback to update job progress
	progressCallback := func(current, total int, message string) {
		jw.jobService.mutex.Lock()
		if total > 0 {
			job.Progress = int(float64(current) / float64(total) * 100)
		}
		jw.jobService.mutex.Unlock()
	}

	// Execute the actual scraping job
	err := jw.jobService.scraperManager.ExecuteJob(ctx, job, progressCallback)

	jw.jobService.mutex.Lock()
	completedTime := time.Now().UTC()
	job.CompletedAt = &completedTime
	job.Progress = 100

	if err != nil {
		job.Status = model.JobStatusFailed
		job.ErrorMsg = err.Error()
	} else {
		job.Status = model.JobStatusCompleted
		// ResultURL and ResultCount are already set by the scraper manager
	}
	jw.jobService.mutex.Unlock()
}
