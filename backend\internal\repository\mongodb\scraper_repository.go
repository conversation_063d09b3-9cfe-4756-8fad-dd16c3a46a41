package mongodb

import (
	"context"
	"fmt"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/model"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ScraperRepository struct {
	pagesCollection   *mongo.Collection
	dataCollection    *mongo.Collection
	resultsCollection *mongo.Collection
}

func NewScraperRepository() *ScraperRepository {
	return &ScraperRepository{
		pagesCollection:   config.GetCollection("scraped_pages"),
		dataCollection:    config.GetCollection("scraped_data"),
		resultsCollection: config.GetCollection("scraping_results"),
	}
}

// CreateScrapedPage creates a new scraped page record
func (r *ScraperRepository) CreateScrapedPage(ctx context.Context, page *model.ScrapedPage) error {
	page.BeforeInsert()
	
	result, err := r.pagesCollection.InsertOne(ctx, page)
	if err != nil {
		return fmt.Errorf("failed to create scraped page: %w", err)
	}
	
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		page.ID = oid
	}
	
	return nil
}

// CreateScrapedData creates a new scraped data record
func (r *ScraperRepository) CreateScrapedData(ctx context.Context, data *model.ScrapedData) error {
	data.BeforeInsert()
	
	result, err := r.dataCollection.InsertOne(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to create scraped data: %w", err)
	}
	
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		data.ID = oid
	}
	
	return nil
}

// GetScrapedPagesByJobID retrieves scraped pages for a specific job
func (r *ScraperRepository) GetScrapedPagesByJobID(ctx context.Context, jobID string, limit, offset int) ([]*model.ScrapedPage, error) {
	filter := bson.M{"job_id": jobID}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "scraped_at", Value: -1}})
	
	cursor, err := r.pagesCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get scraped pages by job ID: %w", err)
	}
	defer cursor.Close(ctx)
	
	var pages []*model.ScrapedPage
	for cursor.Next(ctx) {
		var page model.ScrapedPage
		if err := cursor.Decode(&page); err != nil {
			return nil, fmt.Errorf("failed to decode scraped page: %w", err)
		}
		pages = append(pages, &page)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return pages, nil
}

// GetScrapedPageByID retrieves a scraped page by ID
func (r *ScraperRepository) GetScrapedPageByID(ctx context.Context, pageID uuid.UUID) (*model.ScrapedPage, error) {
	var page model.ScrapedPage
	filter := bson.M{"uuid": pageID}
	
	err := r.pagesCollection.FindOne(ctx, filter).Decode(&page)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("scraped page not found")
		}
		return nil, fmt.Errorf("failed to get scraped page by ID: %w", err)
	}
	
	return &page, nil
}

// GetScrapedDataByPageID retrieves scraped data for a specific page
func (r *ScraperRepository) GetScrapedDataByPageID(ctx context.Context, pageID uuid.UUID) ([]*model.ScrapedData, error) {
	filter := bson.M{"page_id": pageID}
	
	opts := options.Find().SetSort(bson.D{{Key: "position_index", Value: 1}})
	cursor, err := r.dataCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get scraped data by page ID: %w", err)
	}
	defer cursor.Close(ctx)
	
	var dataList []*model.ScrapedData
	for cursor.Next(ctx) {
		var data model.ScrapedData
		if err := cursor.Decode(&data); err != nil {
			return nil, fmt.Errorf("failed to decode scraped data: %w", err)
		}
		dataList = append(dataList, &data)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return dataList, nil
}

// GetScrapedDataByJobID retrieves all scraped data for a specific job
func (r *ScraperRepository) GetScrapedDataByJobID(ctx context.Context, jobID string, limit, offset int) ([]*model.ScrapedData, error) {
	// First get all page IDs for the job
	pageFilter := bson.M{"job_id": jobID}
	pageProjection := bson.M{"uuid": 1}
	
	pageCursor, err := r.pagesCollection.Find(ctx, pageFilter, options.Find().SetProjection(pageProjection))
	if err != nil {
		return nil, fmt.Errorf("failed to get pages for job: %w", err)
	}
	defer pageCursor.Close(ctx)
	
	var pageIDs []uuid.UUID
	for pageCursor.Next(ctx) {
		var page struct {
			UUID uuid.UUID `bson:"uuid"`
		}
		if err := pageCursor.Decode(&page); err != nil {
			return nil, fmt.Errorf("failed to decode page ID: %w", err)
		}
		pageIDs = append(pageIDs, page.UUID)
	}
	
	if len(pageIDs) == 0 {
		return []*model.ScrapedData{}, nil
	}
	
	// Now get scraped data for all these pages
	dataFilter := bson.M{"page_id": bson.M{"$in": pageIDs}}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "position_index", Value: 1}})
	
	dataCursor, err := r.dataCollection.Find(ctx, dataFilter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get scraped data by job ID: %w", err)
	}
	defer dataCursor.Close(ctx)
	
	var dataList []*model.ScrapedData
	for dataCursor.Next(ctx) {
		var data model.ScrapedData
		if err := dataCursor.Decode(&data); err != nil {
			return nil, fmt.Errorf("failed to decode scraped data: %w", err)
		}
		dataList = append(dataList, &data)
	}
	
	if err := dataCursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return dataList, nil
}

// UpdateScrapedPage updates a scraped page
func (r *ScraperRepository) UpdateScrapedPage(ctx context.Context, page *model.ScrapedPage) error {
	filter := bson.M{"uuid": page.UUID}
	update := bson.M{"$set": page}
	
	result, err := r.pagesCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update scraped page: %w", err)
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("scraped page not found")
	}
	
	return nil
}

// DeleteScrapedPagesByJobID deletes all scraped pages for a job
func (r *ScraperRepository) DeleteScrapedPagesByJobID(ctx context.Context, jobID string) error {
	// First get all page IDs for the job
	pageFilter := bson.M{"job_id": jobID}
	pageProjection := bson.M{"uuid": 1}
	
	pageCursor, err := r.pagesCollection.Find(ctx, pageFilter, options.Find().SetProjection(pageProjection))
	if err != nil {
		return fmt.Errorf("failed to get pages for job: %w", err)
	}
	defer pageCursor.Close(ctx)
	
	var pageIDs []uuid.UUID
	for pageCursor.Next(ctx) {
		var page struct {
			UUID uuid.UUID `bson:"uuid"`
		}
		if err := pageCursor.Decode(&page); err != nil {
			return fmt.Errorf("failed to decode page ID: %w", err)
		}
		pageIDs = append(pageIDs, page.UUID)
	}
	
	// Delete scraped data for all pages
	if len(pageIDs) > 0 {
		dataFilter := bson.M{"page_id": bson.M{"$in": pageIDs}}
		_, err = r.dataCollection.DeleteMany(ctx, dataFilter)
		if err != nil {
			return fmt.Errorf("failed to delete scraped data: %w", err)
		}
	}
	
	// Delete scraped pages
	_, err = r.pagesCollection.DeleteMany(ctx, pageFilter)
	if err != nil {
		return fmt.Errorf("failed to delete scraped pages: %w", err)
	}
	
	return nil
}

// CountScrapedPagesByJobID returns the number of scraped pages for a job
func (r *ScraperRepository) CountScrapedPagesByJobID(ctx context.Context, jobID string) (int64, error) {
	filter := bson.M{"job_id": jobID}
	
	count, err := r.pagesCollection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count scraped pages: %w", err)
	}
	
	return count, nil
}

// CountScrapedDataByJobID returns the number of scraped data records for a job
func (r *ScraperRepository) CountScrapedDataByJobID(ctx context.Context, jobID string) (int64, error) {
	// First get all page IDs for the job
	pageFilter := bson.M{"job_id": jobID}
	pageProjection := bson.M{"uuid": 1}
	
	pageCursor, err := r.pagesCollection.Find(ctx, pageFilter, options.Find().SetProjection(pageProjection))
	if err != nil {
		return 0, fmt.Errorf("failed to get pages for job: %w", err)
	}
	defer pageCursor.Close(ctx)
	
	var pageIDs []uuid.UUID
	for pageCursor.Next(ctx) {
		var page struct {
			UUID uuid.UUID `bson:"uuid"`
		}
		if err := pageCursor.Decode(&page); err != nil {
			return 0, fmt.Errorf("failed to decode page ID: %w", err)
		}
		pageIDs = append(pageIDs, page.UUID)
	}
	
	if len(pageIDs) == 0 {
		return 0, nil
	}
	
	// Count scraped data for all these pages
	dataFilter := bson.M{"page_id": bson.M{"$in": pageIDs}}
	
	count, err := r.dataCollection.CountDocuments(ctx, dataFilter)
	if err != nil {
		return 0, fmt.Errorf("failed to count scraped data: %w", err)
	}
	
	return count, nil
}

// DeleteOldScrapedData deletes scraped data older than the specified duration
func (r *ScraperRepository) DeleteOldScrapedData(ctx context.Context, olderThan time.Duration) error {
	cutoff := time.Now().UTC().Add(-olderThan)
	
	// Get old page IDs
	pageFilter := bson.M{"scraped_at": bson.M{"$lt": cutoff}}
	pageProjection := bson.M{"uuid": 1}
	
	pageCursor, err := r.pagesCollection.Find(ctx, pageFilter, options.Find().SetProjection(pageProjection))
	if err != nil {
		return fmt.Errorf("failed to get old pages: %w", err)
	}
	defer pageCursor.Close(ctx)
	
	var pageIDs []uuid.UUID
	for pageCursor.Next(ctx) {
		var page struct {
			UUID uuid.UUID `bson:"uuid"`
		}
		if err := pageCursor.Decode(&page); err != nil {
			return fmt.Errorf("failed to decode page ID: %w", err)
		}
		pageIDs = append(pageIDs, page.UUID)
	}
	
	// Delete scraped data for old pages
	if len(pageIDs) > 0 {
		dataFilter := bson.M{"page_id": bson.M{"$in": pageIDs}}
		_, err = r.dataCollection.DeleteMany(ctx, dataFilter)
		if err != nil {
			return fmt.Errorf("failed to delete old scraped data: %w", err)
		}
	}
	
	// Delete old pages
	_, err = r.pagesCollection.DeleteMany(ctx, pageFilter)
	if err != nil {
		return fmt.Errorf("failed to delete old scraped pages: %w", err)
	}
	
	return nil
}

// CreateScrapingResult creates a new comprehensive scraping result
func (r *ScraperRepository) CreateScrapingResult(ctx context.Context, result *model.ScrapingResult) error {
	result.BeforeInsert()

	_, err := r.resultsCollection.InsertOne(ctx, result)
	if err != nil {
		return fmt.Errorf("failed to create scraping result: %w", err)
	}

	return nil
}

// GetScrapingResultByID retrieves a scraping result by its UUID
func (r *ScraperRepository) GetScrapingResultByID(ctx context.Context, id uuid.UUID) (*model.ScrapingResult, error) {
	var result model.ScrapingResult

	filter := bson.M{"uuid": id}
	err := r.resultsCollection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("scraping result not found")
		}
		return nil, fmt.Errorf("failed to get scraping result: %w", err)
	}

	return &result, nil
}

// GetScrapingResultsByJobID retrieves all scraping results for a job
func (r *ScraperRepository) GetScrapingResultsByJobID(ctx context.Context, jobID string, limit, offset int) ([]*model.ScrapingResult, error) {
	filter := bson.M{"job_id": jobID}

	opts := options.Find().
		SetLimit(int64(limit)).
		SetSkip(int64(offset)).
		SetSort(bson.D{{Key: "scraped_at", Value: -1}})

	cursor, err := r.resultsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find scraping results: %w", err)
	}
	defer cursor.Close(ctx)

	var results []*model.ScrapingResult
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode scraping results: %w", err)
	}

	return results, nil
}

// CountScrapingResultsByJobID counts scraping results for a job
func (r *ScraperRepository) CountScrapingResultsByJobID(ctx context.Context, jobID string) (int64, error) {
	filter := bson.M{"job_id": jobID}

	count, err := r.resultsCollection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count scraping results: %w", err)
	}

	return count, nil
}

// DeleteOldScrapingResults removes scraping results older than the specified duration
func (r *ScraperRepository) DeleteOldScrapingResults(ctx context.Context, maxAge time.Duration) error {
	cutoff := time.Now().Add(-maxAge)
	filter := bson.M{"scraped_at": bson.M{"$lt": cutoff}}

	result, err := r.resultsCollection.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete old scraping results: %w", err)
	}

	if result.DeletedCount > 0 {
		fmt.Printf("Deleted %d old scraping results\n", result.DeletedCount)
	}

	return nil
}

// DeleteOldScrapedPages removes scraped pages older than the specified duration
func (r *ScraperRepository) DeleteOldScrapedPages(ctx context.Context, maxAge time.Duration) error {
	cutoff := time.Now().Add(-maxAge)
	filter := bson.M{"scraped_at": bson.M{"$lt": cutoff}}

	result, err := r.pagesCollection.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete old scraped pages: %w", err)
	}

	if result.DeletedCount > 0 {
		fmt.Printf("Deleted %d old scraped pages\n", result.DeletedCount)
	}

	return nil
}
