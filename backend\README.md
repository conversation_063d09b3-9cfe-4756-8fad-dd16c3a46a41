# Go REST API

This project is a simple REST API built with Go. It provides endpoints to manage items, allowing users to create and retrieve items.

## Project Structure

```
go-rest-api
├── cmd
│   └── main.go          # Entry point of the application
├── internal
│   ├── handler
│   │   └── handler.go   # HTTP request handlers
│   ├── model
│   │   └── model.go     # Data structures
│   └── router
│       └── router.go    # HTTP routes setup
├── go.mod                # Module definition and dependencies
├── go.sum                # Dependency checksums
└── README.md             # Project documentation
```

## Getting Started

### Prerequisites

- Go 1.16 or later

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/go-rest-api.git
   cd go-rest-api
   ```

2. Install dependencies:
   ```
   go mod tidy
   ```

### Running the API

To run the API, execute the following command:

```
go run cmd/main.go
```

The server will start on `localhost:8080`.

### API Endpoints

- `GET /items` - Retrieve a list of items
- `POST /items` - Create a new item

### Contributing

Feel free to submit issues or pull requests for any improvements or features you'd like to see!

### License

This project is licensed under the MIT License.