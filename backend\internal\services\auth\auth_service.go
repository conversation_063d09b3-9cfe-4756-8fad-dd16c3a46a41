package auth

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/model"
	"go-rest-api/internal/repository"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// AuthService handles authentication operations
type AuthService struct {
	config *config.Config
	repos  *repository.Repositories
}

// NewAuthService creates a new AuthService
func NewAuthService(config *config.Config, repos *repository.Repositories) *AuthService {
	return &AuthService{
		config: config,
		repos:  repos,
	}
}

// TokenPair represents access and refresh tokens
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	TokenType    string `json:"token_type"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email      string `json:"email" binding:"required,email"`
	Password   string `json:"password" binding:"required,min=6"`
	DeviceInfo string `json:"device_info,omitempty"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=8"`
	FirstName string `json:"first_name" binding:"required"`
	LastName  string `json:"last_name" binding:"required"`
}

// RefreshRequest represents a token refresh request
type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// Claims represents JWT claims
type Claims struct {
	UserID uuid.UUID      `json:"user_id"`
	Email  string         `json:"email"`
	Role   model.UserRole `json:"role"`
	jwt.RegisteredClaims
}

// Login authenticates a user and returns tokens
func (s *AuthService) Login(req LoginRequest, ipAddress string) (*TokenPair, error) {
	ctx := context.Background()

	// Get user by email
	user, err := s.repos.User.GetByEmail(ctx, req.Email)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return nil, fmt.Errorf("invalid credentials")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if account is locked
	if user.LockedUntil != nil && user.LockedUntil.After(time.Now()) {
		return nil, fmt.Errorf("account is locked until %v", user.LockedUntil)
	}

	// Check if account is active
	if !user.IsActive {
		return nil, fmt.Errorf("account is disabled")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		// Increment failed login attempts
		if err := s.repos.User.IncrementFailedLoginAttempts(ctx, user.UUID); err != nil {
			// Log error but don't fail the request
		}

		// Lock account if too many failed attempts
		if user.FailedLoginAttempts+1 >= s.config.Security.MaxLoginAttempts {
			lockUntil := time.Now().Add(s.config.Security.AccountLockoutDuration)
			if err := s.repos.User.LockUser(ctx, user.UUID, lockUntil); err != nil {
				// Log error but don't fail the request
			}
		}

		return nil, fmt.Errorf("invalid credentials")
	}

	// Reset failed login attempts on successful login
	if err := s.repos.User.ResetFailedLoginAttempts(ctx, user.UUID); err != nil {
		// Log error but don't fail the request
	}

	// Update last login
	if err := s.repos.User.UpdateLastLogin(ctx, user.UUID); err != nil {
		// Log error but don't fail the request
	}

	// Generate tokens
	return s.generateTokenPair(user, req.DeviceInfo, ipAddress)
}

// Register creates a new user account
func (s *AuthService) Register(req RegisterRequest) (*model.User, error) {
	ctx := context.Background()

	// Check if user already exists
	if _, err := s.repos.User.GetByEmail(ctx, req.Email); err == nil {
		return nil, fmt.Errorf("user with email %s already exists", req.Email)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), s.config.Security.BcryptCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &model.User{
		Email:        req.Email,
		PasswordHash: string(hashedPassword),
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Role:         model.UserRoleUser,
		IsActive:     true,
	}

	if err := s.repos.User.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// RefreshToken refreshes an access token using a refresh token
func (s *AuthService) RefreshToken(req RefreshRequest, ipAddress string) (*TokenPair, error) {
	ctx := context.Background()

	// Hash the refresh token
	tokenHash := s.hashToken(req.RefreshToken)

	// Get refresh token from database
	refreshToken, err := s.repos.RefreshToken.GetByTokenHash(ctx, tokenHash)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token")
	}

	// Check if token is expired
	if refreshToken.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("refresh token expired")
	}

	// Get user for token generation
	user, err := s.repos.User.GetByID(ctx, refreshToken.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	// Generate new token pair
	return s.generateTokenPair(user, refreshToken.DeviceInfo, ipAddress)
}

// Logout revokes all refresh tokens for a user
func (s *AuthService) Logout(userID uuid.UUID) error {
	ctx := context.Background()
	return s.repos.RefreshToken.RevokeAllUserTokens(ctx, userID)
}

// ValidateAccessToken validates an access token and returns claims
func (s *AuthService) ValidateAccessToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// generateTokenPair generates access and refresh tokens for a user
func (s *AuthService) generateTokenPair(user *model.User, deviceInfo, ipAddress string) (*TokenPair, error) {
	// Generate access token
	accessToken, err := s.generateAccessToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Generate refresh token
	refreshToken, err := s.generateRefreshToken(user, deviceInfo, ipAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.config.JWT.AccessTokenDuration.Seconds()),
		TokenType:    "Bearer",
	}, nil
}

// generateAccessToken generates a JWT access token
func (s *AuthService) generateAccessToken(user *model.User) (string, error) {
	claims := &Claims{
		UserID: user.UUID,
		Email:  user.Email,
		Role:   user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.config.JWT.AccessTokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "truedax-scraper",
			Subject:   user.UUID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.JWT.Secret))
}

// generateRefreshToken generates a refresh token and stores it in the database
func (s *AuthService) generateRefreshToken(user *model.User, deviceInfo, ipAddress string) (string, error) {
	ctx := context.Background()

	// Generate random token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", fmt.Errorf("failed to generate random token: %w", err)
	}
	token := hex.EncodeToString(tokenBytes)

	// Hash token for storage
	tokenHash := s.hashToken(token)

	// Create refresh token record
	refreshToken := &model.RefreshToken{
		UserID:     user.UUID,
		TokenHash:  tokenHash,
		ExpiresAt:  time.Now().Add(s.config.JWT.RefreshTokenDuration),
		DeviceInfo: deviceInfo,
		IPAddress:  ipAddress,
	}

	if err := s.repos.RefreshToken.Create(ctx, refreshToken); err != nil {
		return "", fmt.Errorf("failed to store refresh token: %w", err)
	}

	return token, nil
}

// hashToken creates a SHA-256 hash of a token
func (s *AuthService) hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}
