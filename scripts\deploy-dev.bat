@echo off
REM TrueDax Web Scraper - Windows Development Deployment Script

echo 🚀 Starting TrueDax Web Scraper Development Deployment...

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "data\results" mkdir data\results
if not exist "data\logs" mkdir data\logs
if not exist "nginx\ssl" mkdir nginx\ssl

REM Copy environment file if it doesn't exist
if not exist ".env" (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your configuration before running in production!
)

REM Build and start services
echo 🔨 Building and starting services...
docker-compose up --build -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 15 /nobreak >nul

REM Check health
echo 🏥 Checking service health...
set /a attempts=0
:healthcheck
set /a attempts+=1
curl -f http://localhost:9000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Service is healthy!
    goto :success
)
if %attempts% geq 30 (
    echo ❌ Service failed to start properly
    docker-compose logs truedax-scraper
    pause
    exit /b 1
)
echo ⏳ Waiting for service... (attempt %attempts%/30)
timeout /t 2 /nobreak >nul
goto :healthcheck

:success
echo.
echo 🎉 TrueDax Web Scraper is now running!
echo.
echo 📋 Service Information:
echo    API URL: http://localhost:9000
echo    Health Check: http://localhost:9000/health
echo    Swagger Docs: http://localhost:9000/swagger/index.html
echo.
echo 🔧 Management Commands:
echo    View logs: docker-compose logs -f truedax-scraper
echo    Stop services: docker-compose down
echo    Restart: docker-compose restart
echo.
echo 📊 Quick Test:
echo    curl http://localhost:9000/health
echo.

REM Display running containers
echo 🐳 Running containers:
docker-compose ps

pause
