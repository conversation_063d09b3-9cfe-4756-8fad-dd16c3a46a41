package model

import (
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AuditLog represents an audit log entry
type AuditLog struct {
	ID           primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UUID         uuid.UUID          `json:"uuid" bson:"uuid"`
	UserID       *uuid.UUID         `json:"user_id,omitempty" bson:"user_id,omitempty"`
	Action       string             `json:"action" bson:"action"`
	ResourceType string             `json:"resource_type,omitempty" bson:"resource_type,omitempty"`
	ResourceID   string             `json:"resource_id,omitempty" bson:"resource_id,omitempty"`
	Details      interface{}        `json:"details,omitempty" bson:"details,omitempty"`
	IPAddress    string             `json:"ip_address,omitempty" bson:"ip_address,omitempty"`
	UserAgent    string             `json:"user_agent,omitempty" bson:"user_agent,omitempty"`
	CreatedAt    time.Time          `json:"created_at" bson:"created_at"`
}

// SystemLog represents a system log entry
type SystemLog struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UUID      uuid.UUID          `json:"uuid" bson:"uuid"`
	Level     string             `json:"level" bson:"level"`
	Message   string             `json:"message" bson:"message"`
	Component string             `json:"component,omitempty" bson:"component,omitempty"`
	Details   interface{}        `json:"details,omitempty" bson:"details,omitempty"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
}

// BeforeInsert sets default values before inserting
func (al *AuditLog) BeforeInsert() {
	if al.UUID == uuid.Nil {
		al.UUID = uuid.New()
	}
	if al.CreatedAt.IsZero() {
		al.CreatedAt = time.Now().UTC()
	}
}

// BeforeInsert sets default values before inserting
func (sl *SystemLog) BeforeInsert() {
	if sl.UUID == uuid.Nil {
		sl.UUID = uuid.New()
	}
	if sl.CreatedAt.IsZero() {
		sl.CreatedAt = time.Now().UTC()
	}
}

// LogLevel constants for system logs
const (
	LogLevelDebug   = "debug"
	LogLevelInfo    = "info"
	LogLevelWarning = "warning"
	LogLevelError   = "error"
	LogLevelFatal   = "fatal"
)

// AuditAction constants for common audit actions
const (
	AuditActionLogin          = "login"
	AuditActionLogout         = "logout"
	AuditActionRegister       = "register"
	AuditActionPasswordReset  = "password_reset"
	AuditActionJobCreate      = "job_create"
	AuditActionJobUpdate      = "job_update"
	AuditActionJobDelete      = "job_delete"
	AuditActionJobStart       = "job_start"
	AuditActionJobStop        = "job_stop"
	AuditActionUserCreate     = "user_create"
	AuditActionUserUpdate     = "user_update"
	AuditActionUserDelete     = "user_delete"
	AuditActionRoleChange     = "role_change"
)
