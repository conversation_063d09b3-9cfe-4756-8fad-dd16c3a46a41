package router

import (
	"net/http"

	"go-rest-api/config"
	"go-rest-api/internal/handler"
	"go-rest-api/internal/middleware"
	"go-rest-api/internal/repository"
	"go-rest-api/internal/services/audit"
	"go-rest-api/internal/services/job"
	"go-rest-api/internal/services/monitoring"

	"github.com/gin-gonic/gin"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"

	_ "go-rest-api/docs"
)

// HealthResponse represents the health check response
type HealthResponse struct {
    Status string `json:"status" example:"ok"`
}

// HealthCheck godoc
// @Summary      Health check
// @Description  Check if the API is running
// @Tags         health
// @Accept       json
// @Produce      json
// @Success      200 {object} HealthResponse
// @Router       /health [get]
func HealthCheck(c *gin.Context) {
    c.JSON(http.StatusOK, HealthResponse{Status: "ok"})
}

func SetupRouter(cfg *config.Config, repos *repository.Repositories) *gin.Engine {
	r := gin.Default()

	// Initialize services and handlers
	auditService := audit.NewAuditService(repos)
	monitoringService := monitoring.NewMonitoringService(repos)
	authHandler := handler.NewAuthHandler(cfg, repos)
	authMiddleware := middleware.NewAuthMiddleware(cfg, repos)
	auditMiddleware := middleware.NewAuditMiddleware(auditService)
	loggingMiddleware := middleware.NewLoggingMiddleware()
	adminHandler := handler.NewAdminHandler(repos)
	healthHandler := handler.NewHealthHandler(monitoringService)
	jobService := job.NewJobService(repos)
	jobHandler := handler.NewJobHandler(jobService)

	// Global middleware
	r.Use(loggingMiddleware.RequestLogger())
	r.Use(loggingMiddleware.ErrorLogger())
	r.Use(gin.Recovery())

	// Public endpoints
	r.GET("/health", healthHandler.HealthCheck)
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Authentication endpoints
	auth := r.Group("/auth")
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/register", authHandler.Register)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/logout", authMiddleware.JWTAuthMiddleware(), authHandler.Logout)
	}

	// Protected group
	protected := r.Group("/")
	protected.Use(authMiddleware.JWTAuthMiddleware())
	protected.Use(auditMiddleware.AuditLogger())

	// Job management endpoints
	jobs := protected.Group("/jobs")
	{
		jobs.POST("", jobHandler.CreateJob)
		jobs.GET("", jobHandler.GetJobs)
		jobs.GET("/stats", jobHandler.GetJobStats)
		jobs.POST("/test-scrape", jobHandler.TestScrapeURL)
		jobs.GET("/:id", jobHandler.GetJob)
		jobs.PUT("/:id", jobHandler.UpdateJob)
		jobs.DELETE("/:id", jobHandler.DeleteJob)
		jobs.POST("/:id/cancel", jobHandler.CancelJob)
		jobs.GET("/:id/queue-position", jobHandler.GetQueuePosition)
		jobs.GET("/:id/results", jobHandler.GetJobResults)
	}

	// Admin-only endpoints
	admin := protected.Group("/admin")
	admin.Use(authMiddleware.RequireAdmin())
	{
		admin.GET("/users", adminHandler.GetUsers)
		admin.GET("/audit-logs", adminHandler.GetAuditLogs)
		admin.GET("/system-logs", adminHandler.GetSystemLogs)
	}

	return r
}