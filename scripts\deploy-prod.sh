#!/bin/bash

# TrueDax Web Scraper - Production Deployment Script

set -e

echo "🚀 Starting TrueDax Web Scraper Production Deployment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it from .env.example and configure it properly."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data/results
mkdir -p data/logs
mkdir -p nginx/ssl

# SSL Certificate check
if [ ! -f nginx/ssl/cert.pem ] || [ ! -f nginx/ssl/key.pem ]; then
    echo "⚠️  SSL certificates not found. Creating self-signed certificates for testing..."
    echo "   For production, replace with proper SSL certificates!"
    
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/key.pem \
        -out nginx/ssl/cert.pem \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost" \
        2>/dev/null || echo "⚠️  OpenSSL not available. SSL will be disabled."
fi

# Build and start services with production profile
echo "🔨 Building and starting production services..."
docker-compose --profile production up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 15

# Check health
echo "🏥 Checking service health..."
for i in {1..30}; do
    if curl -f http://localhost/health > /dev/null 2>&1; then
        echo "✅ Service is healthy!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Service failed to start properly"
        docker-compose logs truedax-scraper
        exit 1
    fi
    echo "⏳ Waiting for service... (attempt $i/30)"
    sleep 2
done

echo ""
echo "🎉 TrueDax Web Scraper is now running in PRODUCTION mode!"
echo ""
echo "📋 Service Information:"
echo "   API URL: http://localhost (with Nginx proxy)"
echo "   Health Check: http://localhost/health"
echo "   Swagger Docs: http://localhost/swagger/index.html"
echo ""
echo "🔒 Security Notes:"
echo "   - JWT authentication is enabled"
echo "   - Rate limiting is active"
echo "   - SSL certificates should be properly configured"
echo ""
echo "🔧 Management Commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop services: docker-compose --profile production down"
echo "   Restart: docker-compose --profile production restart"
echo ""
echo "📊 Quick Test:"
echo "   curl http://localhost/health"
echo ""

# Display running containers
echo "🐳 Running containers:"
docker-compose ps

echo ""
echo "⚠️  IMPORTANT: This is a production deployment!"
echo "   - Monitor logs regularly"
echo "   - Set up proper backup for results data"
echo "   - Configure proper SSL certificates"
echo "   - Monitor resource usage"
