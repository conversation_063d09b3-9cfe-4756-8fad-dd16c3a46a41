#!/bin/bash

# TrueDax Web Scraper - Development Deployment Script

set -e

echo "🚀 Starting TrueDax Web Scraper Development Deployment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data/results
mkdir -p data/logs
mkdir -p nginx/ssl

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before running in production!"
fi

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check health
echo "🏥 Checking service health..."
for i in {1..30}; do
    if curl -f http://localhost:9000/health > /dev/null 2>&1; then
        echo "✅ Service is healthy!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Service failed to start properly"
        docker-compose logs truedax-scraper
        exit 1
    fi
    echo "⏳ Waiting for service... (attempt $i/30)"
    sleep 2
done

echo ""
echo "🎉 TrueDax Web Scraper is now running!"
echo ""
echo "📋 Service Information:"
echo "   API URL: http://localhost:9000"
echo "   Health Check: http://localhost:9000/health"
echo "   Swagger Docs: http://localhost:9000/swagger/index.html"
echo ""
echo "🔧 Management Commands:"
echo "   View logs: docker-compose logs -f truedax-scraper"
echo "   Stop services: docker-compose down"
echo "   Restart: docker-compose restart"
echo ""
echo "📊 Quick Test:"
echo "   curl http://localhost:9000/health"
echo ""

# Display running containers
echo "🐳 Running containers:"
docker-compose ps
