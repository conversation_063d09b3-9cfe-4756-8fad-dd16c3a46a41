basePath: /
definitions:
  handler.ErrorResponse:
    properties:
      error:
        example: Email required
        type: string
    type: object
  handler.JobListResponse:
    properties:
      jobs:
        items:
          $ref: '#/definitions/model.JobSummary'
        type: array
      limit:
        example: 10
        type: integer
      page:
        example: 1
        type: integer
      total:
        example: 150
        type: integer
      total_pages:
        example: 15
        type: integer
    type: object
  handler.LoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  handler.LoginResponse:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  handler.MessageResponse:
    properties:
      message:
        example: Operation completed successfully
        type: string
    type: object
  handler.QueuePositionResponse:
    properties:
      job_id:
        example: job_123456
        type: string
      position:
        example: 3
        type: integer
    type: object
  handler.TestScrapeRequest:
    properties:
      javascript_enabled:
        example: false
        type: boolean
      selectors:
        additionalProperties:
          type: string
        example:
          '"content"': '"p"}'
          '{"title"': '"h1"'
        type: object
      url:
        example: https://example.com
        type: string
    required:
    - url
    type: object
  model.CreateJobRequest:
    properties:
      config:
        $ref: '#/definitions/model.ScrapingConfig'
      description:
        example: Scrape product data
        type: string
      name:
        example: Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 2
    required:
    - config
    - name
    type: object
  model.Item:
    properties:
      id:
        example: "1"
        type: string
      name:
        example: Sample Item
        type: string
    type: object
  model.Job:
    properties:
      completed_at:
        example: "2023-07-18T11:00:00Z"
        type: string
      config:
        $ref: '#/definitions/model.ScrapingConfig'
      created_at:
        example: "2023-07-18T10:30:00Z"
        type: string
      description:
        example: Scrape product information from e-commerce site
        type: string
      error_msg:
        example: Connection timeout
        type: string
      id:
        example: job_123456
        type: string
      name:
        example: E-commerce Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 2
      progress:
        example: 75
        type: integer
      result_count:
        example: 150
        type: integer
      result_url:
        example: https://storage.example.com/results/job_123456.json
        type: string
      started_at:
        example: "2023-07-18T10:35:00Z"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/model.JobStatus'
        example: pending
      user_email:
        example: <EMAIL>
        type: string
    required:
    - name
    type: object
  model.JobPriority:
    enum:
    - 1
    - 2
    - 3
    - 4
    type: integer
    x-enum-varnames:
    - PriorityLow
    - PriorityNormal
    - PriorityHigh
    - PriorityCritical
  model.JobStats:
    properties:
      completed_jobs:
        example: 1200
        type: integer
      failed_jobs:
        example: 42
        type: integer
      pending_jobs:
        example: 5
        type: integer
      running_jobs:
        example: 3
        type: integer
      total_jobs:
        example: 1250
        type: integer
    type: object
  model.JobStatus:
    enum:
    - pending
    - queued
    - running
    - completed
    - failed
    - cancelled
    type: string
    x-enum-varnames:
    - JobStatusPending
    - JobStatusQueued
    - JobStatusRunning
    - JobStatusCompleted
    - JobStatusFailed
    - JobStatusCancelled
  model.JobSummary:
    properties:
      created_at:
        example: "2023-07-18T10:30:00Z"
        type: string
      id:
        example: job_123456
        type: string
      name:
        example: Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 2
      progress:
        example: 75
        type: integer
      result_count:
        example: 150
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/model.JobStatus'
        example: running
    type: object
  model.ScrapingConfig:
    properties:
      delay_ms:
        example: 1000
        type: integer
      headers:
        additionalProperties:
          type: string
        type: object
      javascript_enabled:
        example: false
        type: boolean
      max_pages:
        example: 10
        type: integer
      selectors:
        additionalProperties:
          type: string
        example:
          '"price"': '".price"}'
          '{"title"': '"h1"'
        type: object
      timeout:
        example: 30
        type: integer
      url:
        example: https://example.com
        type: string
      user_agent:
        example: Mozilla/5.0...
        type: string
    required:
    - url
    type: object
  model.UpdateJobRequest:
    properties:
      config:
        $ref: '#/definitions/model.ScrapingConfig'
      description:
        example: Updated description
        type: string
      name:
        example: Updated Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 3
    type: object
  router.HealthResponse:
    properties:
      status:
        example: ok
        type: string
    type: object
host: localhost:9000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: A REST API for web scraping operations
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: TrueDax Web Scraper API
  version: "1.0"
paths:
  /health:
    get:
      consumes:
      - application/json
      description: Check if the API is running
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/router.HealthResponse'
      summary: Health check
      tags:
      - health
  /items:
    get:
      consumes:
      - application/json
      description: Retrieve all items from the system
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/model.Item'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all items
      tags:
      - items
    post:
      consumes:
      - application/json
      description: Add a new item to the system
      parameters:
      - description: Item to create
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/model.Item'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/model.Item'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new item
      tags:
      - items
  /jobs:
    get:
      consumes:
      - application/json
      description: Retrieve a paginated list of user's scraping jobs
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.JobListResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user's jobs
      tags:
      - jobs
    post:
      consumes:
      - application/json
      description: Create a new web scraping job with specified configuration
      parameters:
      - description: Job configuration
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/model.CreateJobRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/model.Job'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new scraping job
      tags:
      - jobs
  /jobs/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a completed, failed, or cancelled scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.MessageResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete a job
      tags:
      - jobs
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Job'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get a specific job
      tags:
      - jobs
    put:
      consumes:
      - application/json
      description: Update configuration of a pending scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated job configuration
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/model.UpdateJobRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Job'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update a job
      tags:
      - jobs
  /jobs/{id}/cancel:
    post:
      consumes:
      - application/json
      description: Cancel a pending, queued, or running scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.MessageResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Cancel a job
      tags:
      - jobs
  /jobs/{id}/queue-position:
    get:
      consumes:
      - application/json
      description: Get the current position of a job in the execution queue
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.QueuePositionResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get job queue position
      tags:
      - jobs
  /jobs/{id}/results:
    get:
      consumes:
      - application/json
      description: Retrieve the results of a completed scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Scraping results
          schema:
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get job results
      tags:
      - jobs
  /jobs/stats:
    get:
      consumes:
      - application/json
      description: Retrieve statistics about user's scraping jobs
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.JobStats'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get job statistics
      tags:
      - jobs
  /jobs/test-scrape:
    post:
      consumes:
      - application/json
      description: Test scraping functionality on a single URL without creating a
        job
      parameters:
      - description: URL and selectors to test
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handler.TestScrapeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Scraping test results
          schema:
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Test scrape a single URL
      tags:
      - jobs
  /login:
    post:
      consumes:
      - application/json
      description: Authenticate user with email and get JWT token
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handler.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.LoginResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      summary: User login
      tags:
      - auth
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
