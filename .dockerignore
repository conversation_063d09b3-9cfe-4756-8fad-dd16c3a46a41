# Git
.git
.gitignore

# Documentation
*.md
docs/

# Environment files
.env
.env.local
.env.production

# Build artifacts
backend/app.exe
backend/app
*.exe

# Results and logs (these should be mounted as volumes)
backend/results/*
logs/
data/

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Node modules (if any frontend is added later)
node_modules/
npm-debug.log*

# Go build artifacts
vendor/
*.test
*.prof

# IDE files
*.code-workspace
.vscode/settings.json

# Docker
Dockerfile
docker-compose*.yml
.dockerignore
