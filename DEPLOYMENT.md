# TrueDax Web Scraper - Deployment Guide

This guide covers deploying the TrueDax Web Scraper in various environments.

## Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM available
- PostgreSQL 15+ (if not using Docker)

## Development Deployment

### Quick Start

1. **Clone and Setup**
```bash
git clone <repository-url>
cd tdwebscraper
cp .env.example .env
```

2. **Start Services**
```bash
docker-compose up -d
```

3. **Verify Deployment**
```bash
curl http://localhost:9000/health
```

### Environment Configuration

Edit `.env` file with your settings:

```bash
# Server Configuration
SERVER_PORT=9000
GIN_MODE=debug

# Database Configuration
MONGODB_URI=**************************************************************************************************
MONGODB_HOST=mongodb
MONGODB_PORT=27017
MONGODB_DATABASE=truedax_scraper
MONGODB_USERNAME=truedax_user
MONGODB_PASSWORD=truedax_secure_password_2024

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d
```

## Production Deployment

### 1. Security Configuration

**Update Environment Variables:**
```bash
# Use strong, unique passwords
MONGODB_PASSWORD=<strong-random-password>
JWT_SECRET=<strong-random-secret-64-chars>

# Production settings
GIN_MODE=release
LOG_LEVEL=warn
BCRYPT_COST=12
```

**SSL/TLS Setup:**
```bash
# Add SSL certificates to nginx/ssl/
# Update nginx/nginx.conf for HTTPS
```

### 2. Database Setup

**Option A: Docker MongoDB**
```bash
docker-compose up -d mongodb
```

**Option B: External MongoDB**
```bash
# Update .env with external database credentials
MONGODB_URI=************************************************************************************
MONGODB_HOST=your-mongodb-host
MONGODB_PORT=27017
MONGODB_USERNAME=your-db-user
MONGODB_PASSWORD=your-db-password
```

### 3. Application Deployment

**Docker Deployment:**
```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d

# Check logs
docker-compose logs -f app
```

**Manual Deployment:**
```bash
# Build application
cd backend
go build -o bin/truedax-scraper cmd/main.go

# Run with systemd service
sudo systemctl start truedax-scraper
sudo systemctl enable truedax-scraper
```

## Kubernetes Deployment

### 1. Create Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: truedax-scraper
```

### 2. ConfigMap and Secrets
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: truedax-secrets
  namespace: truedax-scraper
type: Opaque
stringData:
  DB_PASSWORD: "your-db-password"
  JWT_SECRET: "your-jwt-secret"
```

### 3. Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: truedax-scraper
  namespace: truedax-scraper
spec:
  replicas: 3
  selector:
    matchLabels:
      app: truedax-scraper
  template:
    metadata:
      labels:
        app: truedax-scraper
    spec:
      containers:
      - name: truedax-scraper
        image: truedax/web-scraper:latest
        ports:
        - containerPort: 9000
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: truedax-secrets
              key: DB_PASSWORD
        livenessProbe:
          httpGet:
            path: /live
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Monitoring and Maintenance

### Health Checks

- **Health**: `GET /health` (includes database connectivity check)

### Log Management

**View Logs:**
```bash
# Docker logs
docker-compose logs -f app

# Application logs (JSON format)
tail -f /var/log/truedax-scraper/app.log
```

**Log Rotation:**
```bash
# Configure logrotate
sudo nano /etc/logrotate.d/truedax-scraper
```

### Database Maintenance

**Backup:**
```bash
# Automated backup script
docker exec truedax-mongodb mongodump --uri="****************************************************************************************************" --out=/backup
```

**Cleanup Old Logs:**
```bash
# The application automatically cleans up old logs based on retention settings
# Manual cleanup if needed:
docker exec truedax-mongodb mongosh --eval "use truedax_scraper; db.audit_logs.deleteMany({created_at: {\$lt: new Date(Date.now() - 90*24*60*60*1000)}})"
```

## Scaling

### Horizontal Scaling

1. **Load Balancer Setup**
```nginx
upstream truedax_backend {
    server app1:9000;
    server app2:9000;
    server app3:9000;
}
```

2. **Database Connection Pooling**
```bash
# Increase connection limits in .env
MONGODB_MAX_POOL_SIZE=100
MONGODB_MIN_POOL_SIZE=10
```

### Vertical Scaling

**Resource Limits:**
```yaml
resources:
  limits:
    memory: "2Gi"
    cpu: "1000m"
  requests:
    memory: "1Gi"
    cpu: "500m"
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
```bash
# Check database status
docker-compose ps mongodb
docker-compose logs mongodb

# Test connection
docker exec -it truedax-mongodb mongosh --eval "use truedax_scraper; db.runCommand('ping')"
```

2. **Application Won't Start**
```bash
# Check application logs
docker-compose logs app

# Verify environment variables
docker-compose exec app env | grep -E "(MONGODB_|JWT_|SERVER_)"
```

3. **High Memory Usage**
```bash
# Check metrics endpoint
curl http://localhost:9000/metrics

# Monitor with docker stats
docker stats truedax-app
```

### Performance Tuning

1. **Database Optimization**
```sql
-- Add indexes for frequently queried columns
CREATE INDEX CONCURRENTLY idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX CONCURRENTLY idx_jobs_status ON jobs(status);
```

2. **Application Tuning**
```bash
# Adjust worker pools
MAX_CONCURRENT_JOBS=10

# Tune garbage collection
GOGC=100
```

## Security Checklist

- [ ] Strong, unique passwords for all services
- [ ] JWT secrets are cryptographically secure
- [ ] SSL/TLS certificates are valid and up-to-date
- [ ] Database access is restricted to application only
- [ ] Regular security updates applied
- [ ] Audit logs are monitored
- [ ] Rate limiting is configured
- [ ] Input validation is enabled

## Backup and Recovery

### Automated Backups
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
docker exec truedax-mongodb mongodump --uri="****************************************************************************************************" --out="/backup/backup_${DATE}"
```

### Recovery
```bash
# Restore from backup
docker exec truedax-mongodb mongorestore --uri="****************************************************************************************************" "/backup/backup_20240728_120000/truedax_scraper"
```
