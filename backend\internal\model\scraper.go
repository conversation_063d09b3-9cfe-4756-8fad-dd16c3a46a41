package model

import (
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ScrapedPage represents a scraped web page
type ScrapedPage struct {
	ID            primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UUID          uuid.UUID          `json:"uuid" bson:"uuid"`
	JobID         string             `json:"job_id" bson:"job_id"`
	URL           string             `json:"url" bson:"url"`
	Title         string             `json:"title,omitempty" bson:"title,omitempty"`
	StatusCode    int                `json:"status_code" bson:"status_code"`
	ContentType   string             `json:"content_type,omitempty" bson:"content_type,omitempty"`
	ContentLength int64              `json:"content_length" bson:"content_length"`
	ScrapedAt     time.Time          `json:"scraped_at" bson:"scraped_at"`
	ErrorMsg      string             `json:"error_msg,omitempty" bson:"error_msg,omitempty"`
	ScrapedData   []ScrapedData      `json:"scraped_data,omitempty" bson:"scraped_data,omitempty"`
}

// ScrapedData represents data extracted from a scraped page
type ScrapedData struct {
	ID            primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UUID          uuid.UUID          `json:"uuid" bson:"uuid"`
	PageID        uuid.UUID          `json:"page_id" bson:"page_id"`
	Selector      string             `json:"selector" bson:"selector"`
	FieldName     string             `json:"field_name" bson:"field_name"`
	Value         string             `json:"value" bson:"value"`
	DataType      string             `json:"data_type" bson:"data_type"`
	PositionIndex int                `json:"position_index,omitempty" bson:"position_index,omitempty"`
}

// BeforeInsert sets default values before inserting
func (sp *ScrapedPage) BeforeInsert() {
	if sp.UUID == uuid.Nil {
		sp.UUID = uuid.New()
	}
	if sp.ScrapedAt.IsZero() {
		sp.ScrapedAt = time.Now().UTC()
	}
}

// BeforeInsert sets default values before inserting
func (sd *ScrapedData) BeforeInsert() {
	if sd.UUID == uuid.Nil {
		sd.UUID = uuid.New()
	}
	if sd.DataType == "" {
		sd.DataType = "text"
	}
}

// DataType constants for scraped data
const (
	DataTypeText     = "text"
	DataTypeNumber   = "number"
	DataTypeURL      = "url"
	DataTypeEmail    = "email"
	DataTypeDate     = "date"
	DataTypeImage    = "image"
	DataTypeBoolean  = "boolean"
	DataTypeJSON     = "json"
)

// IsSuccessful checks if the page was scraped successfully
func (sp *ScrapedPage) IsSuccessful() bool {
	return sp.StatusCode >= 200 && sp.StatusCode < 300 && sp.ErrorMsg == ""
}

// HasData checks if the page has any scraped data
func (sp *ScrapedPage) HasData() bool {
	return len(sp.ScrapedData) > 0
}

// ScrapingResult represents a comprehensive scraping result stored in database
type ScrapingResult struct {
	ID          primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UUID        uuid.UUID          `json:"uuid" bson:"uuid"`
	JobID       string             `json:"job_id" bson:"job_id"`
	URL         string             `json:"url" bson:"url"`
	Title       string             `json:"title,omitempty" bson:"title,omitempty"`
	Description string             `json:"description,omitempty" bson:"description,omitempty"`
	StatusCode  int                `json:"status_code" bson:"status_code"`
	Success     bool               `json:"success" bson:"success"`
	ErrorMsg    string             `json:"error_msg,omitempty" bson:"error_msg,omitempty"`
	ScrapedAt   time.Time          `json:"scraped_at" bson:"scraped_at"`

	// Comprehensive content extraction
	Content     ScrapingContent    `json:"content" bson:"content"`
	Metadata    ScrapingMetadata   `json:"metadata" bson:"metadata"`
}

// ScrapingContent represents all extracted content from a page
type ScrapingContent struct {
	Headings        []HeadingData    `json:"headings,omitempty" bson:"headings,omitempty"`
	Paragraphs      []string         `json:"paragraphs,omitempty" bson:"paragraphs,omitempty"`
	Links           []LinkData       `json:"links,omitempty" bson:"links,omitempty"`
	Images          []ImageData      `json:"images,omitempty" bson:"images,omitempty"`
	Lists           []ListData       `json:"lists,omitempty" bson:"lists,omitempty"`
	Tables          []TableData      `json:"tables,omitempty" bson:"tables,omitempty"`
	Forms           []FormData       `json:"forms,omitempty" bson:"forms,omitempty"`
	StructuredData  interface{}      `json:"structured_data,omitempty" bson:"structured_data,omitempty"`
	MainContent     string           `json:"main_content,omitempty" bson:"main_content,omitempty"`
	AllText         string           `json:"all_text,omitempty" bson:"all_text,omitempty"`
}

// ScrapingMetadata represents metadata about the scraping process
type ScrapingMetadata struct {
	UserAgent       string            `json:"user_agent,omitempty" bson:"user_agent,omitempty"`
	ResponseTime    int64             `json:"response_time_ms,omitempty" bson:"response_time_ms,omitempty"`
	ContentLength   int64             `json:"content_length,omitempty" bson:"content_length,omitempty"`
	ContentType     string            `json:"content_type,omitempty" bson:"content_type,omitempty"`
	Language        string            `json:"language,omitempty" bson:"language,omitempty"`
	Charset         string            `json:"charset,omitempty" bson:"charset,omitempty"`
	LastModified    *time.Time        `json:"last_modified,omitempty" bson:"last_modified,omitempty"`
	Headers         map[string]string `json:"headers,omitempty" bson:"headers,omitempty"`
	JavaScriptUsed  bool              `json:"javascript_used" bson:"javascript_used"`
}

// HeadingData represents extracted heading information
type HeadingData struct {
	Level int    `json:"level" bson:"level"`
	Text  string `json:"text" bson:"text"`
}

// LinkData represents extracted link information
type LinkData struct {
	URL    string `json:"url" bson:"url"`
	Text   string `json:"text" bson:"text"`
	Title  string `json:"title,omitempty" bson:"title,omitempty"`
	Target string `json:"target,omitempty" bson:"target,omitempty"`
}

// ImageData represents extracted image information
type ImageData struct {
	URL    string `json:"url" bson:"url"`
	Alt    string `json:"alt,omitempty" bson:"alt,omitempty"`
	Title  string `json:"title,omitempty" bson:"title,omitempty"`
	Width  string `json:"width,omitempty" bson:"width,omitempty"`
	Height string `json:"height,omitempty" bson:"height,omitempty"`
}

// ListData represents extracted list information
type ListData struct {
	Type  string   `json:"type" bson:"type"` // "ordered" or "unordered"
	Items []string `json:"items" bson:"items"`
}

// TableData represents extracted table information
type TableData struct {
	Headers []string   `json:"headers,omitempty" bson:"headers,omitempty"`
	Rows    [][]string `json:"rows,omitempty" bson:"rows,omitempty"`
}

// FormData represents extracted form information
type FormData struct {
	Action string      `json:"action,omitempty" bson:"action,omitempty"`
	Method string      `json:"method,omitempty" bson:"method,omitempty"`
	Fields []FieldData `json:"fields,omitempty" bson:"fields,omitempty"`
}

// FieldData represents form field information
type FieldData struct {
	Name        string `json:"name,omitempty" bson:"name,omitempty"`
	Type        string `json:"type,omitempty" bson:"type,omitempty"`
	Value       string `json:"value,omitempty" bson:"value,omitempty"`
	Placeholder string `json:"placeholder,omitempty" bson:"placeholder,omitempty"`
	Required    bool   `json:"required" bson:"required"`
}

// BeforeInsert sets default values before inserting
func (sr *ScrapingResult) BeforeInsert() {
	if sr.UUID == uuid.Nil {
		sr.UUID = uuid.New()
	}
	if sr.ScrapedAt.IsZero() {
		sr.ScrapedAt = time.Now().UTC()
	}
}
