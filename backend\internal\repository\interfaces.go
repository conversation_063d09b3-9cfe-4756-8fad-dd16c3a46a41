package repository

import (
	"context"
	"time"

	"go-rest-api/internal/model"

	"github.com/google/uuid"
)

// UserRepository defines the interface for user data operations
type UserRepository interface {
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
	GetByAPIKey(ctx context.Context, apiKey string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*model.User, error)
	Count(ctx context.Context) (int64, error)
	UpdateLastLogin(ctx context.Context, id uuid.UUID) error
	IncrementFailedLoginAttempts(ctx context.Context, id uuid.UUID) error
	ResetFailedLoginAttempts(ctx context.Context, id uuid.UUID) error
	LockUser(ctx context.Context, id uuid.UUID, until time.Time) error
}

// RefreshTokenRepository defines the interface for refresh token operations
type RefreshTokenRepository interface {
	Create(ctx context.Context, token *model.RefreshToken) error
	GetByTokenHash(ctx context.Context, tokenHash string) (*model.RefreshToken, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*model.RefreshToken, error)
	RevokeToken(ctx context.Context, tokenHash string) error
	RevokeAllUserTokens(ctx context.Context, userID uuid.UUID) error
	DeleteExpiredTokens(ctx context.Context) error
	CleanupRevokedTokens(ctx context.Context, olderThan time.Duration) error
	Count(ctx context.Context) (int64, error)
	CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
}

// JobRepository defines the interface for job data operations
type JobRepository interface {
	Create(ctx context.Context, job *model.Job) error
	GetByID(ctx context.Context, id string) (*model.Job, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*model.Job, error)
	Update(ctx context.Context, job *model.Job) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, limit, offset int, status *model.JobStatus) ([]*model.Job, error)
	Count(ctx context.Context) (int64, error)
	CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
	CountByStatus(ctx context.Context, status model.JobStatus) (int64, error)
	UpdateStatus(ctx context.Context, id string, status model.JobStatus) error
	UpdateProgress(ctx context.Context, id string, progress int) error
	GetPendingJobs(ctx context.Context, limit int) ([]*model.Job, error)
}

// ScraperRepository defines the interface for scraped page and data operations
type ScraperRepository interface {
	CreateScrapedPage(ctx context.Context, page *model.ScrapedPage) error
	CreateScrapedData(ctx context.Context, data *model.ScrapedData) error
	GetScrapedPagesByJobID(ctx context.Context, jobID string, limit, offset int) ([]*model.ScrapedPage, error)
	GetScrapedPageByID(ctx context.Context, pageID uuid.UUID) (*model.ScrapedPage, error)
	GetScrapedDataByPageID(ctx context.Context, pageID uuid.UUID) ([]*model.ScrapedData, error)
	GetScrapedDataByJobID(ctx context.Context, jobID string, limit, offset int) ([]*model.ScrapedData, error)
	UpdateScrapedPage(ctx context.Context, page *model.ScrapedPage) error
	DeleteScrapedPagesByJobID(ctx context.Context, jobID string) error
	CountScrapedPagesByJobID(ctx context.Context, jobID string) (int64, error)
	CountScrapedDataByJobID(ctx context.Context, jobID string) (int64, error)
	DeleteOldScrapedData(ctx context.Context, olderThan time.Duration) error

	// New comprehensive scraping result methods
	CreateScrapingResult(ctx context.Context, result *model.ScrapingResult) error
	GetScrapingResultByID(ctx context.Context, id uuid.UUID) (*model.ScrapingResult, error)
	GetScrapingResultsByJobID(ctx context.Context, jobID string, limit, offset int) ([]*model.ScrapingResult, error)
	CountScrapingResultsByJobID(ctx context.Context, jobID string) (int64, error)
	DeleteOldScrapingResults(ctx context.Context, maxAge time.Duration) error
	DeleteOldScrapedPages(ctx context.Context, maxAge time.Duration) error
}

// AuditRepository defines the interface for audit and system log operations
type AuditRepository interface {
	CreateAuditLog(ctx context.Context, log *model.AuditLog) error
	CreateSystemLog(ctx context.Context, log *model.SystemLog) error
	GetAuditLogsByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*model.AuditLog, error)
	GetAuditLogsByAction(ctx context.Context, action string, limit, offset int) ([]*model.AuditLog, error)
	GetAuditLogsByDateRange(ctx context.Context, startDate, endDate time.Time, limit, offset int) ([]*model.AuditLog, error)
	GetSystemLogsByLevel(ctx context.Context, level string, limit, offset int) ([]*model.SystemLog, error)
	GetSystemLogsByComponent(ctx context.Context, component string, limit, offset int) ([]*model.SystemLog, error)
	DeleteOldAuditLogs(ctx context.Context, olderThan time.Duration) error
	DeleteOldSystemLogs(ctx context.Context, olderThan time.Duration) error
	CountAuditLogs(ctx context.Context) (int64, error)
	CountSystemLogs(ctx context.Context) (int64, error)

	// Additional methods used by audit service
	GetUserAuditLogs(ctx context.Context, userEmail string, limit, offset int) ([]*model.AuditLog, error)
	GetAuditLogs(ctx context.Context, limit, offset int) ([]*model.AuditLog, error)
}

// Repositories aggregates all repository interfaces
type Repositories struct {
	User         UserRepository
	RefreshToken RefreshTokenRepository
	Job          JobRepository
	Scraper      ScraperRepository
	Audit        AuditRepository
}
