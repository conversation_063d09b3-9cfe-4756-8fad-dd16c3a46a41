// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/health": {
            "get": {
                "description": "Check if the API is running",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Health check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/router.HealthResponse"
                        }
                    }
                }
            }
        },
        "/items": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve all items from the system",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "items"
                ],
                "summary": "Get all items",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/model.Item"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Add a new item to the system",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "items"
                ],
                "summary": "Create a new item",
                "parameters": [
                    {
                        "description": "Item to create",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.Item"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/model.Item"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/jobs": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve a paginated list of user's scraping jobs",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Get user's jobs",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of items per page",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handler.JobListResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new web scraping job with specified configuration",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Create a new scraping job",
                "parameters": [
                    {
                        "description": "Job configuration",
                        "name": "job",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.CreateJobRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/model.Job"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/jobs/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve statistics about user's scraping jobs",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Get job statistics",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.JobStats"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/jobs/test-scrape": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Test scraping functionality on a single URL without creating a job",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Test scrape a single URL",
                "parameters": [
                    {
                        "description": "URL and selectors to test",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handler.TestScrapeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Scraping test results",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/jobs/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve detailed information about a specific scraping job",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Get a specific job",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Job ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Job"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update configuration of a pending scraping job",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Update a job",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Job ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Updated job configuration",
                        "name": "job",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UpdateJobRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Job"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a completed, failed, or cancelled scraping job",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Delete a job",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Job ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handler.MessageResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/jobs/{id}/cancel": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cancel a pending, queued, or running scraping job",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Cancel a job",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Job ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handler.MessageResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/jobs/{id}/queue-position": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the current position of a job in the execution queue",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Get job queue position",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Job ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handler.QueuePositionResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/jobs/{id}/results": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve the results of a completed scraping job",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "jobs"
                ],
                "summary": "Get job results",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Job ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Scraping results",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/login": {
            "post": {
                "description": "Authenticate user with email and get JWT token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "User login",
                "parameters": [
                    {
                        "description": "Login credentials",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handler.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handler.LoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handler.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "handler.ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string",
                    "example": "Email required"
                }
            }
        },
        "handler.JobListResponse": {
            "type": "object",
            "properties": {
                "jobs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.JobSummary"
                    }
                },
                "limit": {
                    "type": "integer",
                    "example": 10
                },
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "total": {
                    "type": "integer",
                    "example": 150
                },
                "total_pages": {
                    "type": "integer",
                    "example": 15
                }
            }
        },
        "handler.LoginRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                }
            }
        },
        "handler.LoginResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                }
            }
        },
        "handler.MessageResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string",
                    "example": "Operation completed successfully"
                }
            }
        },
        "handler.QueuePositionResponse": {
            "type": "object",
            "properties": {
                "job_id": {
                    "type": "string",
                    "example": "job_123456"
                },
                "position": {
                    "type": "integer",
                    "example": 3
                }
            }
        },
        "handler.TestScrapeRequest": {
            "type": "object",
            "required": [
                "url"
            ],
            "properties": {
                "javascript_enabled": {
                    "type": "boolean",
                    "example": false
                },
                "selectors": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    },
                    "example": {
                        "\"content\"": "\"p\"}",
                        "{\"title\"": "\"h1\""
                    }
                },
                "url": {
                    "type": "string",
                    "example": "https://example.com"
                }
            }
        },
        "model.CreateJobRequest": {
            "type": "object",
            "required": [
                "config",
                "name"
            ],
            "properties": {
                "config": {
                    "$ref": "#/definitions/model.ScrapingConfig"
                },
                "description": {
                    "type": "string",
                    "example": "Scrape product data"
                },
                "name": {
                    "type": "string",
                    "example": "Product Scraper"
                },
                "priority": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.JobPriority"
                        }
                    ],
                    "example": 2
                }
            }
        },
        "model.Item": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "example": "1"
                },
                "name": {
                    "type": "string",
                    "example": "Sample Item"
                }
            }
        },
        "model.Job": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "completed_at": {
                    "type": "string",
                    "example": "2023-07-18T11:00:00Z"
                },
                "config": {
                    "$ref": "#/definitions/model.ScrapingConfig"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-07-18T10:30:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "Scrape product information from e-commerce site"
                },
                "error_msg": {
                    "type": "string",
                    "example": "Connection timeout"
                },
                "id": {
                    "type": "string",
                    "example": "job_123456"
                },
                "name": {
                    "type": "string",
                    "example": "E-commerce Product Scraper"
                },
                "priority": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.JobPriority"
                        }
                    ],
                    "example": 2
                },
                "progress": {
                    "type": "integer",
                    "example": 75
                },
                "result_count": {
                    "type": "integer",
                    "example": 150
                },
                "result_url": {
                    "type": "string",
                    "example": "https://storage.example.com/results/job_123456.json"
                },
                "started_at": {
                    "type": "string",
                    "example": "2023-07-18T10:35:00Z"
                },
                "status": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.JobStatus"
                        }
                    ],
                    "example": "pending"
                },
                "user_email": {
                    "type": "string",
                    "example": "<EMAIL>"
                }
            }
        },
        "model.JobPriority": {
            "type": "integer",
            "enum": [
                1,
                2,
                3,
                4
            ],
            "x-enum-varnames": [
                "PriorityLow",
                "PriorityNormal",
                "PriorityHigh",
                "PriorityCritical"
            ]
        },
        "model.JobStats": {
            "type": "object",
            "properties": {
                "completed_jobs": {
                    "type": "integer",
                    "example": 1200
                },
                "failed_jobs": {
                    "type": "integer",
                    "example": 42
                },
                "pending_jobs": {
                    "type": "integer",
                    "example": 5
                },
                "running_jobs": {
                    "type": "integer",
                    "example": 3
                },
                "total_jobs": {
                    "type": "integer",
                    "example": 1250
                }
            }
        },
        "model.JobStatus": {
            "type": "string",
            "enum": [
                "pending",
                "queued",
                "running",
                "completed",
                "failed",
                "cancelled"
            ],
            "x-enum-varnames": [
                "JobStatusPending",
                "JobStatusQueued",
                "JobStatusRunning",
                "JobStatusCompleted",
                "JobStatusFailed",
                "JobStatusCancelled"
            ]
        },
        "model.JobSummary": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2023-07-18T10:30:00Z"
                },
                "id": {
                    "type": "string",
                    "example": "job_123456"
                },
                "name": {
                    "type": "string",
                    "example": "Product Scraper"
                },
                "priority": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.JobPriority"
                        }
                    ],
                    "example": 2
                },
                "progress": {
                    "type": "integer",
                    "example": 75
                },
                "result_count": {
                    "type": "integer",
                    "example": 150
                },
                "status": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.JobStatus"
                        }
                    ],
                    "example": "running"
                }
            }
        },
        "model.ScrapingConfig": {
            "type": "object",
            "required": [
                "url"
            ],
            "properties": {
                "delay_ms": {
                    "type": "integer",
                    "example": 1000
                },
                "headers": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "javascript_enabled": {
                    "type": "boolean",
                    "example": false
                },
                "max_pages": {
                    "type": "integer",
                    "example": 10
                },
                "selectors": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    },
                    "example": {
                        "\"price\"": "\".price\"}",
                        "{\"title\"": "\"h1\""
                    }
                },
                "timeout": {
                    "type": "integer",
                    "example": 30
                },
                "url": {
                    "type": "string",
                    "example": "https://example.com"
                },
                "user_agent": {
                    "type": "string",
                    "example": "Mozilla/5.0..."
                }
            }
        },
        "model.UpdateJobRequest": {
            "type": "object",
            "properties": {
                "config": {
                    "$ref": "#/definitions/model.ScrapingConfig"
                },
                "description": {
                    "type": "string",
                    "example": "Updated description"
                },
                "name": {
                    "type": "string",
                    "example": "Updated Product Scraper"
                },
                "priority": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.JobPriority"
                        }
                    ],
                    "example": 3
                }
            }
        },
        "router.HealthResponse": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "example": "ok"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:9000",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "TrueDax Web Scraper API",
	Description:      "A REST API for web scraping operations",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
