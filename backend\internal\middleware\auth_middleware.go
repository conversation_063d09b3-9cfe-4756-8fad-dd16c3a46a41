package middleware

import (
	"net/http"
	"strings"

	"go-rest-api/internal/services/auth"
	"go-rest-api/config"
	"go-rest-api/internal/model"
	"go-rest-api/internal/repository"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware provides authentication middleware
type AuthMiddleware struct {
	authService *auth.AuthService
}

// NewAuthMiddleware creates a new AuthMiddleware
func NewAuthMiddleware(config *config.Config, repos *repository.Repositories) *AuthMiddleware {
	return &AuthMiddleware{
		authService: auth.NewAuthService(config, repos),
	}
}

// JWTAuthMiddleware validates JWT tokens and sets user context
func (m *AuthMiddleware) JWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing or invalid Authorization header"})
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		claims, err := m.authService.ValidateAccessToken(tokenString)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			return
		}

		// Store user information in context for handlers to use
		c.Set("user_id", claims.UserID)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)
		c.Next()
	}
}

// RequireRole middleware that requires specific user roles
func (m *AuthMiddleware) RequireRole(roles ...model.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		role := userRole.(model.UserRole)
		for _, requiredRole := range roles {
			if role == requiredRole {
				c.Next()
				return
			}
		}

		c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
	}
}

// RequireAdmin middleware that requires admin role
func (m *AuthMiddleware) RequireAdmin() gin.HandlerFunc {
	return m.RequireRole(model.UserRoleAdmin)
}